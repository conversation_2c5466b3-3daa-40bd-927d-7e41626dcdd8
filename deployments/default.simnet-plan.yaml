---
id: 0
name: "Simulated deployment, used as a default for `clarinet console`, `clarinet test` and `clarinet check`"
network: simnet
genesis:
  wallets:
    - name: deployer
      address: ST1PQHQKV0RJXZFY1DGX8MNSNYVE3VGZJSRTPGZGM
      balance: "100000000000000"
      sbtc-balance: "1000000000"
    - name: faucet
      address: STNHKEPYEPJ8ET55ZZ0M5A34J0R3N5FM2CMMMAZ6
      balance: "100000000000000"
      sbtc-balance: "1000000000"
    - name: wallet_1
      address: ST1SJ3DTE5DN7X54YDH5D64R3BCB6A2AG2ZQ8YPD5
      balance: "100000000000000"
      sbtc-balance: "1000000000"
    - name: wallet_2
      address: ST2CY5V39NHDPWSXMW9QDT3HC3GD6Q6XX4CFRK9AG
      balance: "100000000000000"
      sbtc-balance: "1000000000"
    - name: wallet_3
      address: ST2JHG361ZXG51QTKY2NQCVBPPRRE2KZB1HR05NNC
      balance: "100000000000000"
      sbtc-balance: "1000000000"
    - name: wallet_4
      address: ST2NEB84ASENDXKYGJPQW86YXQCEFEX2ZQPG87ND
      balance: "100000000000000"
      sbtc-balance: "1000000000"
    - name: wallet_5
      address: ST2REHHS5J3CERCRBEPMGH7921Q6PYKAADT7JP2VB
      balance: "100000000000000"
      sbtc-balance: "1000000000"
    - name: wallet_6
      address: ST3AM1A56AK2C1XAFJ4115ZSV26EB49BVQ10MGCS0
      balance: "100000000000000"
      sbtc-balance: "1000000000"
    - name: wallet_7
      address: ST3PF13W7Z0RRM42A8VZRVFQ75SV1K26RXEP8YGKJ
      balance: "100000000000000"
      sbtc-balance: "1000000000"
    - name: wallet_8
      address: ST3NBRSFKX28FQ2ZJ1MAKX58HKHSDGNV5N7R21XCP
      balance: "100000000000000"
      sbtc-balance: "1000000000"
  contracts:
    - costs
    - pox
    - pox-2
    - pox-3
    - pox-4
    - lockup
    - costs-2
    - costs-3
    - cost-voting
    - bns
plan:
  batches:
    - id: 0
      transactions:
        - emulated-contract-publish:
            contract-name: proposal
            emulated-sender: ST1PQHQKV0RJXZFY1DGX8MNSNYVE3VGZJSRTPGZGM
            path: contracts/proposal.clar
            clarity-version: 3
        - emulated-contract-publish:
            contract-name: utils
            emulated-sender: ST1PQHQKV0RJXZFY1DGX8MNSNYVE3VGZJSRTPGZGM
            path: contracts/utils.clar
            clarity-version: 3
        - emulated-contract-publish:
            contract-name: dao-factory
            emulated-sender: ST1PQHQKV0RJXZFY1DGX8MNSNYVE3VGZJSRTPGZGM
            path: contracts/dao-factory.clar
            clarity-version: 3
        - emulated-contract-publish:
            contract-name: dao-unifier
            emulated-sender: ST1PQHQKV0RJXZFY1DGX8MNSNYVE3VGZJSRTPGZGM
            path: contracts/dao-unifier.clar
            clarity-version: 3
        - emulated-contract-publish:
            contract-name: funding
            emulated-sender: ST1PQHQKV0RJXZFY1DGX8MNSNYVE3VGZJSRTPGZGM
            path: contracts/funding.clar
            clarity-version: 3
        - emulated-contract-publish:
            contract-name: governance
            emulated-sender: ST1PQHQKV0RJXZFY1DGX8MNSNYVE3VGZJSRTPGZGM
            path: contracts/governance.clar
            clarity-version: 3
        - emulated-contract-publish:
            contract-name: rewards
            emulated-sender: ST1PQHQKV0RJXZFY1DGX8MNSNYVE3VGZJSRTPGZGM
            path: contracts/rewards.clar
            clarity-version: 3
        - emulated-contract-publish:
            contract-name: voting-power
            emulated-sender: ST1PQHQKV0RJXZFY1DGX8MNSNYVE3VGZJSRTPGZGM
            path: contracts/voting-power.clar
            clarity-version: 3
      epoch: "3.1"
