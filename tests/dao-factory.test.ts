import { describe, expect, it, beforeEach } from "vitest";
import { Cl } from "@stacks/transactions";

const accounts = simnet.getAccounts();
const deployer = accounts.get("deployer")!;
const wallet1 = accounts.get("wallet_1")!;
const wallet2 = accounts.get("wallet_2")!;
const wallet3 = accounts.get("wallet_3")!;

describe("DAO Factory Contract Tests", () => {
  beforeEach(() => {
    simnet.setEpoch("3.1");
  });

  describe("DAO Registration", () => {
    it("should register a new DAO successfully", () => {
      const { result } = simnet.callPublicFn(
        "dao-factory",
        "register-dao",
        [
          Cl.contractPrincipal(deployer, "dao-unifier"),
          Cl.stringAscii("Test DAO"),
          Cl.stringUtf8("A test DAO for testing purposes"),
          Cl.standardPrincipal(wallet1),
          Cl.some(Cl.stringUtf8("https://test-dao.com/metadata")),
        ],
        wallet1
      );

      expect(result).toBeOk(Cl.contractPrincipal(deployer, "dao-unifier"));
    });

    it("should prevent duplicate DAO registration", () => {
      // Register first DAO
      simnet.callPublicFn(
        "dao-factory",
        "register-dao",
        [
          Cl.contractPrincipal(deployer, "dao-unifier"),
          Cl.stringAscii("Test DAO"),
          Cl.stringUtf8("A test DAO for testing purposes"),
          Cl.standardPrincipal(wallet1),
          Cl.none(),
        ],
        wallet1
      );

      // Try to register the same DAO again
      const { result } = simnet.callPublicFn(
        "dao-factory",
        "register-dao",
        [
          Cl.contractPrincipal(deployer, "dao-unifier"),
          Cl.stringAscii("Another DAO"),
          Cl.stringUtf8("Another description"),
          Cl.standardPrincipal(wallet2),
          Cl.none(),
        ],
        wallet2
      );

      expect(result).toBeErr(Cl.uint(103)); // ERR_ALREADY_REGISTERED
    });

    it("should increment DAO count after registration", () => {
      const initialCount = simnet.callReadOnlyFn(
        "dao-factory",
        "get-dao-count",
        [],
        deployer
      );
      expect(initialCount.result).toBeUint(0);

      simnet.callPublicFn(
        "dao-factory",
        "register-dao",
        [
          Cl.contractPrincipal(deployer, "dao-unifier"),
          Cl.stringAscii("Test DAO"),
          Cl.stringUtf8("A test DAO for testing purposes"),
          Cl.standardPrincipal(wallet1),
          Cl.none(),
        ],
        wallet1
      );

      const newCount = simnet.callReadOnlyFn(
        "dao-factory",
        "get-dao-count",
        [],
        deployer
      );
      expect(newCount.result).toBeUint(1);
    });
  });

  describe("Proposal Management", () => {
    beforeEach(() => {
      // Register a DAO first
      simnet.callPublicFn(
        "dao-factory",
        "register-dao",
        [
          Cl.contractPrincipal(deployer, "dao-unifier"),
          Cl.stringAscii("Test DAO"),
          Cl.stringUtf8("A test DAO for testing purposes"),
          Cl.standardPrincipal(wallet1),
          Cl.none(),
        ],
        wallet1
      );
    });

    it("should register a proposal for a DAO", () => {
      const { result } = simnet.callPublicFn(
        "dao-factory",
        "submit-proposal",
        [
          Cl.contractPrincipal(deployer, "dao-unifier"), // dao-contract
          Cl.contractPrincipal(deployer, "proposal"), // proposal-contract
        ],
        wallet1
      );

      expect(result).toBeOk(Cl.uint(0)); // First proposal ID
    });

    it("should update proposal metrics", () => {
      // Register a proposal first
      simnet.callPublicFn(
        "dao-factory",
        "submit-proposal",
        [
          Cl.contractPrincipal(deployer, "dao-unifier"),
          Cl.contractPrincipal(deployer, "proposal"),
        ],
        wallet1
      );

      const { result } = simnet.callPublicFn(
        "dao-factory",
        "update-proposal-metrics",
        [
          Cl.contractPrincipal(deployer, "proposal"),
          Cl.uint(10), // vote count
          Cl.uint(1000), // funding amount
          Cl.stringAscii("active"),
        ],
        wallet1
      );

      expect(result).toBeOk(Cl.bool(true));
    });
  });

  describe("Read-only Functions", () => {
    it("should return correct DAO information", () => {
      // Register a DAO
      simnet.callPublicFn(
        "dao-factory",
        "register-dao",
        [
          Cl.contractPrincipal(deployer, "dao-unifier"),
          Cl.stringAscii("Test DAO"),
          Cl.stringUtf8("A test DAO for testing purposes"),
          Cl.standardPrincipal(wallet1),
          Cl.some(Cl.stringUtf8("https://test-dao.com/metadata")),
        ],
        wallet1
      );

      const { result } = simnet.callReadOnlyFn(
        "dao-factory",
        "get-dao",
        [Cl.contractPrincipal(deployer, "dao-unifier")],
        deployer
      );

      // Check that the result is some and has the expected structure
      expect(result).toBeSome();
      const daoInfo = result as any;
      expect(daoInfo.value.data.name.data).toBe("Test DAO");
      expect(daoInfo.value.data.description.data).toBe(
        "A test DAO for testing purposes"
      );
      expect(daoInfo.value.data["token-contract"].address).toBe(wallet1);
      expect(daoInfo.value.data["proposal-count"].value).toBe(0n);
      expect(daoInfo.value.data.active.value).toBe(true);
    });

    it("should return none for non-existent DAO", () => {
      const { result } = simnet.callReadOnlyFn(
        "dao-factory",
        "get-dao",
        [Cl.contractPrincipal(deployer, "non-existent")],
        deployer
      );

      expect(result).toBeNone();
    });
  });
});
