import { describe, expect, it, beforeEach } from "vitest";
import { Cl } from "@stacks/transactions";

const accounts = simnet.getAccounts();
const deployer = accounts.get("deployer")!;
const wallet1 = accounts.get("wallet_1")!;
const wallet2 = accounts.get("wallet_2")!;
const wallet3 = accounts.get("wallet_3")!;

describe("Rewards Contract Tests", () => {
  beforeEach(() => {
    simnet.setEpoch("3.1");
  });

  describe("Reward Pool Creation", () => {
    it("should create a reward pool successfully", () => {
      const { result } = simnet.callPublicFn(
        "rewards",
        "create-reward-pool",
        [
          Cl.standardPrincipal(wallet1), // dao-id
          Cl.none(), // token-contract (STX rewards)
          Cl.uint(10000), // total-allocated
          Cl.uint(10), // reward-per-vote
          Cl.uint(100), // reward-per-proposal
          Cl.uint(200), // participation-multiplier (2x)
          Cl.uint(1000), // pool-duration
        ],
        wallet1
      );

      expect(result).toBeOk(Cl.contractPrincipal(deployer, "rewards"));
    });

    it("should validate reward pool parameters", () => {
      // Test invalid total allocated
      const result1 = simnet.callPublicFn(
        "rewards",
        "create-reward-pool",
        [
          Cl.standardPrincipal(wallet1),
          Cl.none(),
          Cl.uint(0), // Invalid amount
          Cl.uint(10),
          Cl.uint(100),
          Cl.uint(200),
          Cl.uint(1000),
        ],
        wallet1
      );

      expect(result1.result).toBeErr(Cl.uint(302)); // ERR_INVALID_AMOUNT

      // Test invalid pool duration
      const result2 = simnet.callPublicFn(
        "rewards",
        "create-reward-pool",
        [
          Cl.standardPrincipal(wallet1),
          Cl.none(),
          Cl.uint(10000),
          Cl.uint(10),
          Cl.uint(100),
          Cl.uint(200),
          Cl.uint(0), // Invalid duration
        ],
        wallet1
      );

      expect(result2.result).toBeErr(Cl.uint(306)); // ERR_INVALID_PERIOD

      // Test invalid participation multiplier
      const result3 = simnet.callPublicFn(
        "rewards",
        "create-reward-pool",
        [
          Cl.standardPrincipal(wallet1),
          Cl.none(),
          Cl.uint(10000),
          Cl.uint(10),
          Cl.uint(100),
          Cl.uint(1001), // Too high multiplier (>10x)
          Cl.uint(1000),
        ],
        wallet1
      );

      expect(result3.result).toBeErr(Cl.uint(302)); // ERR_INVALID_AMOUNT
    });
  });

  describe("Activity Recording", () => {
    beforeEach(() => {
      // Create a reward pool
      simnet.callPublicFn(
        "rewards",
        "create-reward-pool",
        [
          Cl.standardPrincipal(wallet1),
          Cl.none(),
          Cl.uint(10000),
          Cl.uint(10),
          Cl.uint(100),
          Cl.uint(200),
          Cl.uint(1000),
        ],
        wallet1
      );
    });

    it("should record voting activity", () => {
      const { result } = simnet.callPublicFn(
        "rewards",
        "record-voting-activity",
        [
          Cl.standardPrincipal(wallet1), // dao-id
          Cl.standardPrincipal(wallet2), // user
          Cl.standardPrincipal(wallet3), // proposal-id
        ],
        wallet1
      );

      expect(result).toBeOk(Cl.bool(true));
    });

    it("should record proposal activity", () => {
      const { result } = simnet.callPublicFn(
        "rewards",
        "record-proposal-activity",
        [
          Cl.standardPrincipal(wallet1), // dao-id
          Cl.standardPrincipal(wallet2), // user
          Cl.standardPrincipal(wallet3), // proposal-id
        ],
        wallet1
      );

      expect(result).toBeOk(Cl.bool(true));
    });

    it("should update participation metrics", () => {
      // Record some activities
      simnet.callPublicFn(
        "rewards",
        "record-voting-activity",
        [
          Cl.standardPrincipal(wallet1),
          Cl.standardPrincipal(wallet2),
          Cl.standardPrincipal(wallet3),
        ],
        wallet1
      );

      simnet.callPublicFn(
        "rewards",
        "record-proposal-activity",
        [
          Cl.standardPrincipal(wallet1),
          Cl.standardPrincipal(wallet2),
          Cl.standardPrincipal(wallet3),
        ],
        wallet1
      );

      // Check DAO participation metrics
      const { result } = simnet.callReadOnlyFn(
        "rewards",
        "get-dao-participation-metrics",
        [Cl.standardPrincipal(wallet1)],
        deployer
      );

      expect(result).toBeSome(
        Cl.tuple({
          "total-votes-cast": Cl.uint(1),
          "total-proposals-created": Cl.uint(1),
          "active-participants": Cl.uint(0),
          "reward-periods": Cl.uint(0),
          "last-reward-distribution": Cl.uint(0),
        })
      );
    });
  });

  describe("Reward Calculations", () => {
    beforeEach(() => {
      // Create a reward pool
      simnet.callPublicFn(
        "rewards",
        "create-reward-pool",
        [
          Cl.standardPrincipal(wallet1),
          Cl.none(),
          Cl.uint(10000),
          Cl.uint(10), // 10 per vote
          Cl.uint(100), // 100 per proposal
          Cl.uint(200), // 2x multiplier
          Cl.uint(1000),
        ],
        wallet1
      );

      // Record some activities
      simnet.callPublicFn(
        "rewards",
        "record-voting-activity",
        [
          Cl.standardPrincipal(wallet1),
          Cl.standardPrincipal(wallet2),
          Cl.standardPrincipal(wallet3),
        ],
        wallet1
      );

      simnet.callPublicFn(
        "rewards",
        "record-proposal-activity",
        [
          Cl.standardPrincipal(wallet1),
          Cl.standardPrincipal(wallet2),
          Cl.standardPrincipal(wallet3),
        ],
        wallet1
      );
    });

    it("should calculate voting rewards correctly", () => {
      const { result } = simnet.callReadOnlyFn(
        "rewards",
        "calculate-voting-rewards",
        [
          Cl.standardPrincipal(wallet1), // dao-id
          Cl.standardPrincipal(wallet2), // user
          Cl.uint(5), // votes-cast
        ],
        deployer
      );

      expect(result).toBeUint(50); // 5 votes * 10 reward per vote
    });

    it("should calculate proposal rewards correctly", () => {
      const { result } = simnet.callReadOnlyFn(
        "rewards",
        "calculate-proposal-rewards",
        [
          Cl.standardPrincipal(wallet1), // dao-id
          Cl.standardPrincipal(wallet2), // user
          Cl.uint(3), // proposals-created
        ],
        deployer
      );

      expect(result).toBeUint(300); // 3 proposals * 100 reward per proposal
    });

    it("should calculate participation bonus correctly", () => {
      const { result } = simnet.callReadOnlyFn(
        "rewards",
        "calculate-participation-bonus",
        [
          Cl.standardPrincipal(wallet1), // dao-id
          Cl.standardPrincipal(wallet2), // user
          Cl.uint(100), // participation-score
        ],
        deployer
      );

      expect(result).toBeUint(200); // (100 * 200) / 100 = 200
    });

    it("should calculate and update total rewards", () => {
      const currentPeriod = Math.floor(simnet.blockHeight / 1000);

      const { result } = simnet.callPublicFn(
        "rewards",
        "calculate-and-update-rewards",
        [
          Cl.standardPrincipal(wallet1), // dao-id
          Cl.standardPrincipal(wallet2), // user
          Cl.uint(currentPeriod), // period
        ],
        wallet1
      );

      // Should calculate based on recorded activities
      // 1 vote (10 points) + 1 proposal (50 points) = 60 participation score
      // Voting reward: 1 * 10 = 10
      // Proposal reward: 1 * 100 = 100
      // Participation bonus: (60 * 200) / 100 = 120
      // Total: 10 + 100 + 120 = 230
      expect(result).toBeOk(Cl.uint(230));
    });
  });

  describe("Reward Claims", () => {
    beforeEach(() => {
      // Create reward pool and record activities
      simnet.callPublicFn(
        "rewards",
        "create-reward-pool",
        [
          Cl.standardPrincipal(wallet1),
          Cl.none(),
          Cl.uint(10000),
          Cl.uint(10),
          Cl.uint(100),
          Cl.uint(200),
          Cl.uint(1000),
        ],
        wallet1
      );

      simnet.callPublicFn(
        "rewards",
        "record-voting-activity",
        [
          Cl.standardPrincipal(wallet1),
          Cl.standardPrincipal(wallet2),
          Cl.standardPrincipal(wallet3),
        ],
        wallet1
      );

      const currentPeriod = Math.floor(simnet.blockHeight / 1000);
      simnet.callPublicFn(
        "rewards",
        "calculate-and-update-rewards",
        [
          Cl.standardPrincipal(wallet1),
          Cl.standardPrincipal(wallet2),
          Cl.uint(currentPeriod),
        ],
        wallet1
      );
    });

    it("should claim rewards successfully", () => {
      const currentPeriod = Math.floor(simnet.blockHeight / 1000);

      const { result } = simnet.callPublicFn(
        "rewards",
        "claim-rewards",
        [
          Cl.standardPrincipal(wallet1), // dao-id
          Cl.uint(currentPeriod), // period
          Cl.none(), // token-contract (STX)
        ],
        wallet2
      );

      expect(result).toBeOk(Cl.uint(30)); // Expected reward amount
    });

    it("should prevent double claiming", () => {
      const currentPeriod = Math.floor(simnet.blockHeight / 1000);

      // Claim first time
      simnet.callPublicFn(
        "rewards",
        "claim-rewards",
        [Cl.standardPrincipal(wallet1), Cl.uint(currentPeriod), Cl.none()],
        wallet2
      );

      // Try to claim again
      const { result } = simnet.callPublicFn(
        "rewards",
        "claim-rewards",
        [Cl.standardPrincipal(wallet1), Cl.uint(currentPeriod), Cl.none()],
        wallet2
      );

      expect(result).toBeErr(Cl.uint(304)); // ERR_ALREADY_CLAIMED
    });

    it("should handle non-existent reward claims", () => {
      const { result } = simnet.callPublicFn(
        "rewards",
        "claim-rewards",
        [
          Cl.standardPrincipal(wallet1),
          Cl.uint(999), // Non-existent period
          Cl.none(),
        ],
        wallet3 // User with no rewards
      );

      expect(result).toBeErr(Cl.uint(303)); // ERR_REWARD_NOT_FOUND
    });
  });

  describe("Period Management", () => {
    beforeEach(() => {
      simnet.callPublicFn(
        "rewards",
        "create-reward-pool",
        [
          Cl.standardPrincipal(wallet1),
          Cl.none(),
          Cl.uint(10000),
          Cl.uint(10),
          Cl.uint(100),
          Cl.uint(200),
          Cl.uint(1000),
        ],
        wallet1
      );
    });

    it("should distribute period rewards", () => {
      const currentPeriod = Math.floor(simnet.blockHeight / 1000);

      const { result } = simnet.callPublicFn(
        "rewards",
        "distribute-period-rewards",
        [
          Cl.standardPrincipal(wallet1), // dao-id
          Cl.uint(currentPeriod), // period
        ],
        wallet1
      );

      expect(result).toBeErr(Cl.uint(303)); // ERR_REWARD_NOT_FOUND (period not initialized)
    });
  });

  describe("Read-only Functions", () => {
    beforeEach(() => {
      simnet.callPublicFn(
        "rewards",
        "create-reward-pool",
        [
          Cl.standardPrincipal(wallet1),
          Cl.none(),
          Cl.uint(10000),
          Cl.uint(10),
          Cl.uint(100),
          Cl.uint(200),
          Cl.uint(1000),
        ],
        wallet1
      );
    });

    it("should return reward pool information", () => {
      const { result } = simnet.callReadOnlyFn(
        "rewards",
        "get-reward-pool",
        [Cl.contractPrincipal(deployer, "rewards")],
        deployer
      );

      // Check that the result is some and has the expected structure
      expect(result).toBeSome();
      const poolInfo = result as any;
      expect(poolInfo.value.data["dao-id"].address).toBe(wallet1);
      expect(poolInfo.value.data["total-allocated"].value).toBe(10000n);
      expect(poolInfo.value.data["reward-per-vote"].value).toBe(10n);
      expect(poolInfo.value.data["reward-per-proposal"].value).toBe(100n);
      expect(poolInfo.value.data["participation-multiplier"].value).toBe(200n);
      expect(poolInfo.value.data.active.value).toBe(true);
    });

    it("should return global statistics", () => {
      const totalDistributed = simnet.callReadOnlyFn(
        "rewards",
        "get-total-rewards-distributed",
        [],
        deployer
      );

      const totalClaims = simnet.callReadOnlyFn(
        "rewards",
        "get-total-reward-claims",
        [],
        deployer
      );

      expect(totalDistributed.result).toBeUint(0);
      expect(totalClaims.result).toBeUint(0);
    });
  });
});
