import { describe, expect, it, beforeEach } from "vitest";
import { Cl } from "@stacks/transactions";

const accounts = simnet.getAccounts();
const deployer = accounts.get("deployer")!;
const wallet1 = accounts.get("wallet_1")!;
const wallet2 = accounts.get("wallet_2")!;
const wallet3 = accounts.get("wallet_3")!;

describe("DAO Unifier Contract Tests", () => {
  beforeEach(() => {
    simnet.setEpoch("3.1");
  });

  describe("DAO Creation", () => {
    it("should create a new DAO successfully", () => {
      const { result } = simnet.callPublicFn(
        "dao-unifier",
        "create-dao",
        [
          Cl.stringAscii("Test DAO"),
          Cl.standardPrincipal(wallet1), // token-contract
          Cl.standardPrincipal(wallet2), // dao-contract
        ],
        wallet1
      );

      expect(result).toBeOk(Cl.standardPrincipal(wallet2));
    });

    it("should prevent duplicate DAO creation", () => {
      // Create first DAO
      simnet.callPublicFn(
        "dao-unifier",
        "create-dao",
        [
          Cl.stringAscii("Test DAO"),
          Cl.standardPrincipal(wallet1),
          Cl.standardPrincipal(wallet2),
        ],
        wallet1
      );

      // Try to create with same dao-contract
      const { result } = simnet.callPublicFn(
        "dao-unifier",
        "create-dao",
        [
          Cl.stringAscii("Another DAO"),
          Cl.standardPrincipal(wallet1),
          Cl.standardPrincipal(wallet2), // Same dao-contract
        ],
        wallet1
      );

      expect(result).toBeErr(Cl.uint(104)); // ERR_ALREADY_INITIALIZED
    });

    it("should validate DAO name", () => {
      const { result } = simnet.callPublicFn(
        "dao-unifier",
        "create-dao",
        [
          Cl.stringAscii(""), // Empty name
          Cl.standardPrincipal(wallet1),
          Cl.standardPrincipal(wallet2),
        ],
        wallet1
      );

      expect(result).toBeErr(Cl.uint(101)); // ERR_INVALID_NAME
    });

    it("should increment DAO count", () => {
      const initialCount = simnet.callReadOnlyFn(
        "dao-unifier",
        "get-dao-count",
        [],
        deployer
      );
      expect(initialCount.result).toBeUint(0);

      simnet.callPublicFn(
        "dao-unifier",
        "create-dao",
        [
          Cl.stringAscii("Test DAO"),
          Cl.standardPrincipal(wallet1),
          Cl.standardPrincipal(wallet2),
        ],
        wallet1
      );

      const newCount = simnet.callReadOnlyFn(
        "dao-unifier",
        "get-dao-count",
        [],
        deployer
      );
      expect(newCount.result).toBeUint(1);
    });
  });

  describe("Proposal Registration", () => {
    beforeEach(() => {
      // Create a DAO first
      simnet.callPublicFn(
        "dao-unifier",
        "create-dao",
        [
          Cl.stringAscii("Test DAO"),
          Cl.standardPrincipal(wallet1),
          Cl.standardPrincipal(wallet2),
        ],
        wallet1
      );
    });

    it("should register proposal for DAO", () => {
      const { result } = simnet.callPublicFn(
        "dao-unifier",
        "register-proposal-for-dao",
        [
          Cl.standardPrincipal(wallet2), // dao-id
          Cl.standardPrincipal(wallet3), // proposal-contract
        ],
        wallet1 // DAO owner
      );

      expect(result).toBeOk(Cl.uint(0)); // First proposal ID
    });

    it("should allow DAO contract to register proposals", () => {
      const { result } = simnet.callPublicFn(
        "dao-unifier",
        "register-proposal-for-dao",
        [
          Cl.standardPrincipal(wallet2), // dao-id
          Cl.standardPrincipal(wallet3), // proposal-contract
        ],
        wallet2 // DAO contract itself
      );

      expect(result).toBeOk(Cl.uint(0));
    });

    it("should prevent unauthorized proposal registration", () => {
      const { result } = simnet.callPublicFn(
        "dao-unifier",
        "register-proposal-for-dao",
        [Cl.standardPrincipal(wallet2), Cl.standardPrincipal(wallet3)],
        wallet3 // Not DAO owner or DAO contract
      );

      expect(result).toBeErr(Cl.uint(100)); // ERR_UNAUTHORIZED
    });

    it("should increment proposal counter", () => {
      // Register first proposal
      simnet.callPublicFn(
        "dao-unifier",
        "register-proposal-for-dao",
        [Cl.standardPrincipal(wallet2), Cl.standardPrincipal(wallet3)],
        wallet1
      );

      // Register second proposal
      const { result } = simnet.callPublicFn(
        "dao-unifier",
        "register-proposal-for-dao",
        [
          Cl.standardPrincipal(wallet2),
          Cl.contractPrincipal(deployer, "proposal"),
        ],
        wallet1
      );

      expect(result).toBeOk(Cl.uint(1)); // Second proposal ID
    });

    it("should handle non-existent DAO", () => {
      const { result } = simnet.callPublicFn(
        "dao-unifier",
        "register-proposal-for-dao",
        [
          Cl.standardPrincipal(wallet3), // Non-existent DAO
          Cl.contractPrincipal(deployer, "proposal"),
        ],
        wallet1
      );

      expect(result).toBeErr(Cl.uint(103)); // ERR_DAO_NOT_FOUND
    });
  });

  describe("Read-only Functions", () => {
    beforeEach(() => {
      simnet.callPublicFn(
        "dao-unifier",
        "create-dao",
        [
          Cl.stringAscii("Test DAO"),
          Cl.standardPrincipal(wallet1),
          Cl.standardPrincipal(wallet2),
        ],
        wallet1
      );
    });

    it("should return DAO information", () => {
      const { result } = simnet.callReadOnlyFn(
        "dao-unifier",
        "get-dao-details",
        [Cl.standardPrincipal(wallet2)],
        deployer
      );

      // Check that the result is some and has the expected structure
      expect(result).toBeSome();
      const daoInfo = result as any;
      expect(daoInfo.value.data.name.data).toBe("Test DAO");
      expect(daoInfo.value.data.token.address).toBe(wallet1);
      expect(daoInfo.value.data.owner.address).toBe(wallet1);
      expect(daoInfo.value.data["proposal-counter"].value).toBe(0n);
    });

    it("should return proposal information", () => {
      // Register a proposal first
      simnet.callPublicFn(
        "dao-unifier",
        "register-proposal-for-dao",
        [Cl.standardPrincipal(wallet2), Cl.standardPrincipal(wallet3)],
        wallet1
      );

      const { result } = simnet.callReadOnlyFn(
        "dao-unifier",
        "get-dao-proposal",
        [Cl.standardPrincipal(wallet2), Cl.uint(0)],
        deployer
      );

      expect(result).toBeSome(Cl.standardPrincipal(wallet3));
    });

    it("should return none for non-existent DAO", () => {
      const { result } = simnet.callReadOnlyFn(
        "dao-unifier",
        "get-dao-details",
        [Cl.standardPrincipal(wallet3)],
        deployer
      );

      expect(result).toBeNone();
    });

    it("should return none for non-existent proposal", () => {
      const { result } = simnet.callReadOnlyFn(
        "dao-unifier",
        "get-dao-proposal",
        [Cl.standardPrincipal(wallet2), Cl.uint(999)],
        deployer
      );

      expect(result).toBeNone();
    });
  });

  describe("DAO Trait Implementation", () => {
    beforeEach(() => {
      simnet.callPublicFn(
        "dao-unifier",
        "create-dao",
        [
          Cl.stringAscii("Test DAO"),
          Cl.standardPrincipal(wallet1),
          Cl.standardPrincipal(wallet2),
        ],
        wallet1
      );
    });

    it("should implement get-name function", () => {
      const { result } = simnet.callReadOnlyFn(
        "dao-unifier",
        "impl-get-name",
        [Cl.standardPrincipal(wallet2)],
        deployer
      );

      expect(result).toBeOk(Cl.stringAscii("Test DAO"));
    });

    it("should implement get-token function", () => {
      const { result } = simnet.callReadOnlyFn(
        "dao-unifier",
        "impl-get-token",
        [Cl.standardPrincipal(wallet2)],
        deployer
      );

      expect(result).toBeOk(Cl.standardPrincipal(wallet1));
    });

    it("should implement get-next-proposal-id function", () => {
      const { result } = simnet.callReadOnlyFn(
        "dao-unifier",
        "impl-get-next-proposal-id",
        [Cl.standardPrincipal(wallet2)],
        deployer
      );

      expect(result).toBeOk(Cl.uint(0));
    });

    it("should handle non-existent DAO in trait functions", () => {
      const result1 = simnet.callReadOnlyFn(
        "dao-unifier",
        "impl-get-name",
        [Cl.standardPrincipal(wallet3)],
        deployer
      );

      const result2 = simnet.callReadOnlyFn(
        "dao-unifier",
        "impl-get-token",
        [Cl.standardPrincipal(wallet3)],
        deployer
      );

      const result3 = simnet.callReadOnlyFn(
        "dao-unifier",
        "impl-get-next-proposal-id",
        [Cl.standardPrincipal(wallet3)],
        deployer
      );

      expect(result1.result).toBeErr(Cl.uint(1));
      expect(result2.result).toBeErr(Cl.uint(1));
      expect(result3.result).toBeErr(Cl.uint(1));
    });
  });
});
