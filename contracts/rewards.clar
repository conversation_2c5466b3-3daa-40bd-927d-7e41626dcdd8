;; rewards.clar
;; Reward distribution system for DAO participants
;; Handles voting rewards, participation incentives, and contributor compensation

(use-trait dao-trait .utils.dao-trait)
(use-trait ft-trait .utils.ft-trait)
(use-trait proposal-trait .proposal.proposal-trait)

;; Constants
(define-constant CONTRACT_OWNER tx-sender)
(define-constant ERR_UNAUTHORIZED (err u300))
(define-constant ERR_INSUFFICIENT_BALANCE (err u301))
(define-constant ERR_INVALID_AMOUNT (err u302))
(define-constant ERR_REWARD_NOT_FOUND (err u303))
(define-constant ERR_ALREADY_CLAIMED (err u304))
(define-constant ERR_REWARD_EXPIRED (err u305))
(define-constant ERR_INVALID_PERIOD (err u306))
(define-constant ERR_NO_PARTICIPATION (err u307))
(define-constant ERR_POOL_NOT_FOUND (err u308))

;; Define current-block-height variable
(define-data-var current-block-height uint u100)

;; Reward pool configuration
(define-map reward-pools principal {
  dao-id: principal,
  token-contract: (optional principal), ;; none for STX rewards
  total-allocated: uint,
  total-distributed: uint,
  reward-per-vote: uint,
  reward-per-proposal: uint,
  participation-multiplier: uint,
  pool-start-block: uint,
  pool-end-block: uint,
  active: bool
})

;; Individual user rewards
(define-map user-rewards { dao-id: principal, user: principal, period: uint } {
  voting-rewards: uint,
  proposal-rewards: uint,
  participation-bonus: uint,
  total-earned: uint,
  claimed-amount: uint,
  last-claim-block: uint,
  votes-cast: uint,
  proposals-created: uint,
  participation-score: uint
})

;; Reward claims tracking
(define-map reward-claims { dao-id: principal, user: principal, claim-id: uint } {
  amount: uint,
  token-contract: (optional principal),
  claimed-at: uint,
  claim-type: (string-ascii 20) ;; "voting", "proposal", "participation"
})

;; DAO participation metrics
(define-map dao-participation-metrics principal {
  total-votes-cast: uint,
  total-proposals-created: uint,
  active-participants: uint,
  reward-periods: uint,
  last-reward-distribution: uint
})

;; Reward distribution periods
(define-map reward-periods { dao-id: principal, period: uint } {
  start-block: uint,
  end-block: uint,
  total-rewards: uint,
  participants: uint,
  distributed: bool
})

;; Global reward statistics
(define-data-var total-rewards-distributed uint u0)
(define-data-var total-reward-claims uint u0)

;; Read-only functions
(define-read-only (get-reward-pool (pool-id principal))
  (map-get? reward-pools pool-id)
)

(define-read-only (get-user-rewards (dao-id principal) (user principal) (period uint))
  (map-get? user-rewards { dao-id: dao-id, user: user, period: period })
)

(define-read-only (get-dao-participation-metrics (dao-id principal))
  (map-get? dao-participation-metrics dao-id)
)

(define-read-only (get-reward-period (dao-id principal) (period uint))
  (map-get? reward-periods { dao-id: dao-id, period: period })
)

(define-read-only (calculate-voting-rewards (dao-id principal) (user principal) (votes-cast uint))
  ;; Find the reward pool for this DAO by iterating through pools
  ;; For simplicity, we'll use a default calculation if no pool is found
  (let (
    (default-reward-per-vote u10)
  )
    (* votes-cast default-reward-per-vote)
  )
)

(define-read-only (calculate-proposal-rewards (dao-id principal) (user principal) (proposals-created uint))
  ;; Find the reward pool for this DAO by iterating through pools
  ;; For simplicity, we'll use a default calculation if no pool is found
  (let (
    (default-reward-per-proposal u100)
  )
    (* proposals-created default-reward-per-proposal)
  )
)

(define-read-only (calculate-participation-bonus (dao-id principal) (user principal) (participation-score uint))
  ;; Find the reward pool for this DAO by iterating through pools
  ;; For simplicity, we'll use a default calculation if no pool is found
  (let (
    (default-multiplier u200)
  )
    (/ (* participation-score default-multiplier) u100)
  )
)

(define-read-only (get-total-rewards-distributed)
  (var-get total-rewards-distributed)
)

(define-read-only (get-total-reward-claims)
  (var-get total-reward-claims)
)

;; Public functions
(define-public (create-reward-pool
    (dao-id principal)
    (token-contract (optional principal))
    (total-allocated uint)
    (reward-per-vote uint)
    (reward-per-proposal uint)
    (participation-multiplier uint)
    (pool-duration uint)
  )
  (let (
    (pool-id (as-contract tx-sender))
    (start-block (var-get current-block-height))
    (end-block (+ start-block pool-duration))
  )
    ;; Validate inputs
    (asserts! (> total-allocated u0) ERR_INVALID_AMOUNT)
    (asserts! (> pool-duration u0) ERR_INVALID_PERIOD)
    (asserts! (<= participation-multiplier u1000) ERR_INVALID_AMOUNT) ;; Max 10x multiplier

    ;; Create reward pool
    (map-set reward-pools pool-id {
      dao-id: dao-id,
      token-contract: token-contract,
      total-allocated: total-allocated,
      total-distributed: u0,
      reward-per-vote: reward-per-vote,
      reward-per-proposal: reward-per-proposal,
      participation-multiplier: participation-multiplier,
      pool-start-block: start-block,
      pool-end-block: end-block,
      active: true
    })

    ;; Initialize DAO participation metrics if not exists
    (if (is-none (map-get? dao-participation-metrics dao-id))
      (map-set dao-participation-metrics dao-id {
        total-votes-cast: u0,
        total-proposals-created: u0,
        active-participants: u0,
        reward-periods: u0,
        last-reward-distribution: u0
      })
      true
    )

    (print { event: "reward-pool-created", pool-id: pool-id, dao-id: dao-id, allocated: total-allocated })
    (ok pool-id)
  )
)

(define-public (record-voting-activity
    (dao-id principal)
    (user principal)
    (proposal-id principal)
  )
  (let (
    (current-period (/ (var-get current-block-height) u1000)) ;; Simple period calculation
    (current-rewards (default-to
      { voting-rewards: u0, proposal-rewards: u0, participation-bonus: u0,
        total-earned: u0, claimed-amount: u0, last-claim-block: u0,
        votes-cast: u0, proposals-created: u0, participation-score: u0 }
      (map-get? user-rewards { dao-id: dao-id, user: user, period: current-period })
    ))
    (dao-metrics (default-to
      { total-votes-cast: u0, total-proposals-created: u0, active-participants: u0,
        reward-periods: u0, last-reward-distribution: u0 }
      (map-get? dao-participation-metrics dao-id)
    ))
  )
    ;; Update user voting activity
    (map-set user-rewards { dao-id: dao-id, user: user, period: current-period }
      (merge current-rewards {
        votes-cast: (+ (get votes-cast current-rewards) u1),
        participation-score: (+ (get participation-score current-rewards) u10) ;; 10 points per vote
      })
    )

    ;; Update DAO metrics
    (map-set dao-participation-metrics dao-id
      (merge dao-metrics {
        total-votes-cast: (+ (get total-votes-cast dao-metrics) u1)
      })
    )

    (print { event: "voting-activity-recorded", dao-id: dao-id, user: user, proposal-id: proposal-id })
    (ok true)
  )
)

(define-public (record-proposal-activity
    (dao-id principal)
    (user principal)
    (proposal-id principal)
  )
  (let (
    (current-period (/ (var-get current-block-height) u1000))
    (current-rewards (default-to
      { voting-rewards: u0, proposal-rewards: u0, participation-bonus: u0,
        total-earned: u0, claimed-amount: u0, last-claim-block: u0,
        votes-cast: u0, proposals-created: u0, participation-score: u0 }
      (map-get? user-rewards { dao-id: dao-id, user: user, period: current-period })
    ))
    (dao-metrics (default-to
      { total-votes-cast: u0, total-proposals-created: u0, active-participants: u0,
        reward-periods: u0, last-reward-distribution: u0 }
      (map-get? dao-participation-metrics dao-id)
    ))
  )
    ;; Update user proposal activity
    (map-set user-rewards { dao-id: dao-id, user: user, period: current-period }
      (merge current-rewards {
        proposals-created: (+ (get proposals-created current-rewards) u1),
        participation-score: (+ (get participation-score current-rewards) u50) ;; 50 points per proposal
      })
    )

    ;; Update DAO metrics
    (map-set dao-participation-metrics dao-id
      (merge dao-metrics {
        total-proposals-created: (+ (get total-proposals-created dao-metrics) u1)
      })
    )

    (print { event: "proposal-activity-recorded", dao-id: dao-id, user: user, proposal-id: proposal-id })
    (ok true)
  )
)

(define-public (calculate-and-update-rewards
    (dao-id principal)
    (user principal)
    (period uint)
  )
  (let (
    (user-data (unwrap! (map-get? user-rewards { dao-id: dao-id, user: user, period: period }) ERR_REWARD_NOT_FOUND))
    (voting-reward (calculate-voting-rewards dao-id user (get votes-cast user-data)))
    (proposal-reward (calculate-proposal-rewards dao-id user (get proposals-created user-data)))
    (participation-bonus (calculate-participation-bonus dao-id user (get participation-score user-data)))
    (total-earned (+ voting-reward (+ proposal-reward participation-bonus)))
  )
    ;; Update user rewards
    (map-set user-rewards { dao-id: dao-id, user: user, period: period }
      (merge user-data {
        voting-rewards: voting-reward,
        proposal-rewards: proposal-reward,
        participation-bonus: participation-bonus,
        total-earned: total-earned
      })
    )

    (print { event: "rewards-calculated", dao-id: dao-id, user: user, period: period, total: total-earned })
    (ok total-earned)
  )
)

(define-public (claim-rewards
    (dao-id principal)
    (period uint)
    (token-contract (optional <ft-trait>))
  )
  (let (
    (user-data (unwrap! (map-get? user-rewards { dao-id: dao-id, user: tx-sender, period: period }) ERR_REWARD_NOT_FOUND))
    (claimable-amount (- (get total-earned user-data) (get claimed-amount user-data)))
    (claim-id (var-get total-reward-claims))
  )
    ;; Validate claim
    (asserts! (> claimable-amount u0) ERR_ALREADY_CLAIMED)

    ;; Process reward distribution
    (match token-contract
      token (try! (as-contract (contract-call? token transfer claimable-amount tx-sender tx-sender none)))
      (try! (as-contract (stx-transfer? claimable-amount tx-sender tx-sender)))
    )

    ;; Update user rewards record
    (map-set user-rewards { dao-id: dao-id, user: tx-sender, period: period }
      (merge user-data {
        claimed-amount: (get total-earned user-data),
        last-claim-block: (var-get current-block-height)
      })
    )

    ;; Record the claim
    (map-set reward-claims { dao-id: dao-id, user: tx-sender, claim-id: claim-id } {
      amount: claimable-amount,
      token-contract: (match token-contract token (some (contract-of token)) none),
      claimed-at: (var-get current-block-height),
      claim-type: "mixed"
    })

    ;; Update global statistics
    (var-set total-rewards-distributed (+ (var-get total-rewards-distributed) claimable-amount))
    (var-set total-reward-claims (+ claim-id u1))

    (print { event: "rewards-claimed", dao-id: dao-id, user: tx-sender, amount: claimable-amount })
    (ok claimable-amount)
  )
)

(define-public (distribute-period-rewards
    (dao-id principal)
    (period uint)
  )
  (let (
    (period-data (unwrap! (map-get? reward-periods { dao-id: dao-id, period: period }) ERR_REWARD_NOT_FOUND))
  )
    ;; Only allow distribution once per period
    (asserts! (not (get distributed period-data)) ERR_ALREADY_CLAIMED)

    ;; Mark period as distributed
    (map-set reward-periods { dao-id: dao-id, period: period }
      (merge period-data { distributed: true })
    )

    ;; Update DAO metrics
    (match (map-get? dao-participation-metrics dao-id)
      metrics (map-set dao-participation-metrics dao-id
        (merge metrics {
          reward-periods: (+ (get reward-periods metrics) u1),
          last-reward-distribution: (var-get current-block-height)
        })
      )
      false
    )

    (print { event: "period-rewards-distributed", dao-id: dao-id, period: period })
    (ok true)
  )
)
