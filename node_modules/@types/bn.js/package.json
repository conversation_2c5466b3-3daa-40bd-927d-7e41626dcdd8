{"name": "@types/bn.js", "version": "5.2.0", "description": "TypeScript definitions for bn.js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/bn.js", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "LogvinovLeon", "url": "https://github.com/LogvinovLeon"}, {"name": "<PERSON>", "githubUsername": "HenryNguyen5", "url": "https://github.com/HenryNguyen5"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/Gilthoniel"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/bn.js"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "3f23b10b76f77cd098e1e68dc2c3e9ab0a252a527f471e45c7f023ed47959c43", "typeScriptVersion": "5.1"}