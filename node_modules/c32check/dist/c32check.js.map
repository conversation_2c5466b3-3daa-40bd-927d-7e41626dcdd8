{"version": 3, "file": "c32check.js", "mappings": ";CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAkB,SAAID,IAEtBD,EAAe,SAAIC,GACpB,CATD,CASGK,MAAM,0CCNT,SAASC,EAAOC,GACZ,IAAKC,OAAOC,cAAcF,IAAMA,EAAI,EAChC,MAAM,IAAIG,MAAM,2BAA2BH,IACnD,CAEA,SAASI,EAAKC,GACV,GAAiB,kBAANA,EACP,MAAM,IAAIF,MAAM,yBAAyBE,IACjD,CAEA,SAASC,EAAMD,KAAME,GACjB,KAAMF,aAAaG,YACf,MAAM,IAAIC,UAAU,uBACxB,GAAIF,EAAQG,OAAS,IAAMH,EAAQI,SAASN,EAAEK,QAC1C,MAAM,IAAID,UAAU,iCAAiCF,oBAA0BF,EAAEK,SACzF,CAEA,SAASE,EAAKA,GACV,GAAoB,mBAATA,GAA8C,mBAAhBA,EAAKC,OAC1C,MAAM,IAAIV,MAAM,mDACpBJ,EAAOa,EAAKE,WACZf,EAAOa,EAAKG,SAChB,CAEA,SAASC,EAAOC,EAAUC,GAAgB,GACtC,GAAID,EAASE,UACT,MAAM,IAAIhB,MAAM,oCACpB,GAAIe,GAAiBD,EAASG,SAC1B,MAAM,IAAIjB,MAAM,wCACxB,CAEA,SAASkB,EAAOC,EAAKL,GACjBX,EAAMgB,GACN,MAAMC,EAAMN,EAASH,UACrB,GAAIQ,EAAIZ,OAASa,EACb,MAAM,IAAIpB,MAAM,yDAAyDoB,IAEjF,CAvCAC,OAAOC,eAAe/B,EAAS,aAAc,CAAEgC,OAAO,IACtDhC,EAAQ2B,OAAS3B,EAAQsB,OAAStB,EAAQkB,KAAOlB,EAAQY,MAAQZ,EAAQU,KAAOV,EAAQK,YAAS,EAKjGL,EAAQK,OAASA,EAKjBL,EAAQU,KAAOA,EAOfV,EAAQY,MAAQA,EAOhBZ,EAAQkB,KAAOA,EAOflB,EAAQsB,OAASA,EAQjBtB,EAAQ2B,OAASA,EACjB,MAAMM,EAAS,CACX5B,SACAK,OACAE,QACAM,OACAI,SACAK,UAEJ3B,EAAA,QAAkBiC,iBCjDlBH,OAAOC,eAAe/B,EAAS,aAAc,CAAEgC,OAAO,IACtDhC,EAAQkC,UAAO,EACf,MAAMC,EAAe,EAAQ,KACvBC,EAAa,EAAQ,IAe3B,MAAMF,UAAaE,EAAWC,KAC1BC,YAAYjB,EAAUD,EAAWmB,EAAWC,GACxCC,QACArC,KAAKiB,SAAWA,EAChBjB,KAAKgB,UAAYA,EACjBhB,KAAKmC,UAAYA,EACjBnC,KAAKoC,KAAOA,EACZpC,KAAKsB,UAAW,EAChBtB,KAAKY,OAAS,EACdZ,KAAKsC,IAAM,EACXtC,KAAKqB,WAAY,EACjBrB,KAAKuC,OAAS,IAAI7B,WAAWO,GAC7BjB,KAAKwC,MAAO,EAAIR,EAAWS,YAAYzC,KAAKuC,OAChD,CACAG,OAAOC,GACHZ,EAAaa,QAAQ1B,OAAOlB,MAC5B,MAAM,KAAEwC,EAAI,OAAED,EAAM,SAAEtB,GAAajB,KAE7B6C,GADNF,GAAO,EAAIX,EAAWc,SAASH,IACd/B,OACjB,IAAK,IAAI0B,EAAM,EAAGA,EAAMO,GAAM,CAC1B,MAAME,EAAOC,KAAKvB,IAAIR,EAAWjB,KAAKsC,IAAKO,EAAMP,GAEjD,GAAIS,IAAS9B,EAMbsB,EAAOU,IAAIN,EAAKO,SAASZ,EAAKA,EAAMS,GAAO/C,KAAKsC,KAChDtC,KAAKsC,KAAOS,EACZT,GAAOS,EACH/C,KAAKsC,MAAQrB,IACbjB,KAAKmD,QAAQX,EAAM,GACnBxC,KAAKsC,IAAM,OAXf,CACI,MAAMc,GAAW,EAAIpB,EAAWS,YAAYE,GAC5C,KAAO1B,GAAY4B,EAAMP,EAAKA,GAAOrB,EACjCjB,KAAKmD,QAAQC,EAAUd,EAE/B,CAQJ,CAGA,OAFAtC,KAAKY,QAAU+B,EAAK/B,OACpBZ,KAAKqD,aACErD,IACX,CACAsD,WAAW9B,GACPO,EAAaa,QAAQ1B,OAAOlB,MAC5B+B,EAAaa,QAAQrB,OAAOC,EAAKxB,MACjCA,KAAKsB,UAAW,EAIhB,MAAM,OAAEiB,EAAM,KAAEC,EAAI,SAAEvB,EAAQ,KAAEmB,GAASpC,KACzC,IAAI,IAAEsC,GAAQtC,KAEduC,EAAOD,KAAS,IAChBtC,KAAKuC,OAAOW,SAASZ,GAAKiB,KAAK,GAE3BvD,KAAKmC,UAAYlB,EAAWqB,IAC5BtC,KAAKmD,QAAQX,EAAM,GACnBF,EAAM,GAGV,IAAK,IAAIkB,EAAIlB,EAAKkB,EAAIvC,EAAUuC,IAC5BjB,EAAOiB,GAAK,GAxExB,SAAsBhB,EAAMiB,EAAY7B,EAAOQ,GAC3C,GAAiC,mBAAtBI,EAAKkB,aACZ,OAAOlB,EAAKkB,aAAaD,EAAY7B,EAAOQ,GAChD,MAAMuB,EAAOC,OAAO,IACdC,EAAWD,OAAO,YAClBE,EAAK3D,OAAQyB,GAAS+B,EAAQE,GAC9BE,EAAK5D,OAAOyB,EAAQiC,GACpBG,EAAI5B,EAAO,EAAI,EACf6B,EAAI7B,EAAO,EAAI,EACrBI,EAAK0B,UAAUT,EAAaO,EAAGF,EAAI1B,GACnCI,EAAK0B,UAAUT,EAAaQ,EAAGF,EAAI3B,EACvC,CAiEQsB,CAAalB,EAAMvB,EAAW,EAAG2C,OAAqB,EAAd5D,KAAKY,QAAawB,GAC1DpC,KAAKmD,QAAQX,EAAM,GACnB,MAAM2B,GAAQ,EAAInC,EAAWS,YAAYjB,GACzCxB,KAAKoE,MAAMC,SAAQ,CAACC,EAAGd,IAAMW,EAAMD,UAAU,EAAIV,EAAGc,EAAGlC,IAC3D,CACAmC,SACI,MAAM,OAAEhC,EAAM,UAAEvB,GAAchB,KAC9BA,KAAKsD,WAAWf,GAChB,MAAMiC,EAAMjC,EAAOkC,MAAM,EAAGzD,GAE5B,OADAhB,KAAK0E,UACEF,CACX,CACAG,WAAWC,GACPA,IAAOA,EAAK,IAAI5E,KAAKkC,aACrB0C,EAAG3B,OAAOjD,KAAKoE,OACf,MAAM,SAAEnD,EAAQ,OAAEsB,EAAM,OAAE3B,EAAM,SAAEU,EAAQ,UAAED,EAAS,IAAEiB,GAAQtC,KAO/D,OANA4E,EAAGhE,OAASA,EACZgE,EAAGtC,IAAMA,EACTsC,EAAGtD,SAAWA,EACdsD,EAAGvD,UAAYA,EACXT,EAASK,GACT2D,EAAGrC,OAAOU,IAAIV,GACXqC,CACX,EAEJhF,EAAQkC,KAAOA,eC1GfJ,OAAOC,eAAe/B,EAAS,aAAc,CAAEgC,OAAO,IACtDhC,EAAQiF,YAAS,EACjBjF,EAAQiF,OAAS,CACbC,UAAMC,EACNC,IAAqB,iBAATC,MAAqB,WAAYA,KAAOA,KAAKJ,YAASE,iBCJtErD,OAAOC,eAAe/B,EAAS,aAAc,CAAEgC,OAAO,IACtDhC,EAAQsF,YAAS,EACjB,MAAMC,EAAa,EAAQ,KACrBnD,EAAa,EAAQ,IAIrBoD,EAAM,CAACC,EAAG9E,EAAG+E,IAAOD,EAAI9E,EAAM8E,EAAIC,EAAM/E,EAAI+E,EAI5CC,EAAW,IAAIC,YAAY,CAC7B,WAAY,WAAY,WAAY,WAAY,UAAY,WAAY,WAAY,WACpF,WAAY,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WACpF,WAAY,WAAY,UAAY,UAAY,UAAY,WAAY,WAAY,WACpF,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAY,UACpF,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WACpF,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UACpF,UAAY,UAAY,UAAY,UAAY,UAAY,WAAY,WAAY,WACpF,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,aAIlFC,EAAK,IAAID,YAAY,CACvB,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAY,aAIlFE,EAAW,IAAIF,YAAY,IACjC,MAAMG,UAAeR,EAAWrD,KAC5BI,cACIG,MAAM,GAAI,GAAI,GAAG,GAGjBrC,KAAK4F,EAAY,EAARH,EAAG,GACZzF,KAAK6F,EAAY,EAARJ,EAAG,GACZzF,KAAK8F,EAAY,EAARL,EAAG,GACZzF,KAAK+F,EAAY,EAARN,EAAG,GACZzF,KAAKgG,EAAY,EAARP,EAAG,GACZzF,KAAKiG,EAAY,EAARR,EAAG,GACZzF,KAAKkG,EAAY,EAART,EAAG,GACZzF,KAAKmG,EAAY,EAARV,EAAG,EAChB,CACArB,MACI,MAAM,EAAEwB,EAAC,EAAEC,EAAC,EAAEC,EAAC,EAAEC,EAAC,EAAEC,EAAC,EAAEC,EAAC,EAAEC,EAAC,EAAEC,GAAMnG,KACnC,MAAO,CAAC4F,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EACjC,CAEAlD,IAAI2C,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,GACrBnG,KAAK4F,EAAQ,EAAJA,EACT5F,KAAK6F,EAAQ,EAAJA,EACT7F,KAAK8F,EAAQ,EAAJA,EACT9F,KAAK+F,EAAQ,EAAJA,EACT/F,KAAKgG,EAAQ,EAAJA,EACThG,KAAKiG,EAAQ,EAAJA,EACTjG,KAAKkG,EAAQ,EAAJA,EACTlG,KAAKmG,EAAQ,EAAJA,CACb,CACAhD,QAAQX,EAAM4D,GAEV,IAAK,IAAI5C,EAAI,EAAGA,EAAI,GAAIA,IAAK4C,GAAU,EACnCV,EAASlC,GAAKhB,EAAK6D,UAAUD,GAAQ,GACzC,IAAK,IAAI5C,EAAI,GAAIA,EAAI,GAAIA,IAAK,CAC1B,MAAM8C,EAAMZ,EAASlC,EAAI,IACnB+C,EAAKb,EAASlC,EAAI,GAClBgD,GAAK,EAAIxE,EAAWyE,MAAMH,EAAK,IAAK,EAAItE,EAAWyE,MAAMH,EAAK,IAAOA,IAAQ,EAC7EI,GAAK,EAAI1E,EAAWyE,MAAMF,EAAI,KAAM,EAAIvE,EAAWyE,MAAMF,EAAI,IAAOA,IAAO,GACjFb,EAASlC,GAAMkD,EAAKhB,EAASlC,EAAI,GAAKgD,EAAKd,EAASlC,EAAI,IAAO,CACnE,CAEA,IAAI,EAAEoC,EAAC,EAAEC,EAAC,EAAEC,EAAC,EAAEC,EAAC,EAAEC,EAAC,EAAEC,EAAC,EAAEC,EAAC,EAAEC,GAAMnG,KACjC,IAAK,IAAIwD,EAAI,EAAGA,EAAI,GAAIA,IAAK,CACzB,MACMmD,EAAMR,IADG,EAAInE,EAAWyE,MAAMT,EAAG,IAAK,EAAIhE,EAAWyE,MAAMT,EAAG,KAAM,EAAIhE,EAAWyE,MAAMT,EAAG,OAnEjGX,EAoE4BW,GAAGC,GApEPZ,EAoEUa,GAAKX,EAAS/B,GAAKkC,EAASlC,GAAM,EAE/DoD,IADS,EAAI5E,EAAWyE,MAAMb,EAAG,IAAK,EAAI5D,EAAWyE,MAAMb,EAAG,KAAM,EAAI5D,EAAWyE,MAAMb,EAAG,KAC7ER,EAAIQ,EAAGC,EAAGC,GAAM,EACrCK,EAAID,EACJA,EAAID,EACJA,EAAID,EACJA,EAAKD,EAAIY,EAAM,EACfZ,EAAID,EACJA,EAAID,EACJA,EAAID,EACJA,EAAKe,EAAKC,EAAM,CACpB,CA/EI,IAACvB,EAiFLO,EAAKA,EAAI5F,KAAK4F,EAAK,EACnBC,EAAKA,EAAI7F,KAAK6F,EAAK,EACnBC,EAAKA,EAAI9F,KAAK8F,EAAK,EACnBC,EAAKA,EAAI/F,KAAK+F,EAAK,EACnBC,EAAKA,EAAIhG,KAAKgG,EAAK,EACnBC,EAAKA,EAAIjG,KAAKiG,EAAK,EACnBC,EAAKA,EAAIlG,KAAKkG,EAAK,EACnBC,EAAKA,EAAInG,KAAKmG,EAAK,EACnBnG,KAAKiD,IAAI2C,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAClC,CACA9C,aACIqC,EAASnC,KAAK,EAClB,CACAmB,UACI1E,KAAKiD,IAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAC9BjD,KAAKuC,OAAOgB,KAAK,EACrB,EAMJ3D,EAAQsF,QAAS,EAAIlD,EAAW6E,kBAAiB,IAAM,IAAIlB,kBC3G3DjE,OAAOC,eAAe/B,EAAS,aAAc,CAAEgC,OAAO,IACtDhC,EAAQkH,YAAclH,EAAQmH,wBAA0BnH,EAAQiH,gBAAkBjH,EAAQoH,UAAYpH,EAAQqC,KAAOrC,EAAQqH,YAAcrH,EAAQkD,QAAUlD,EAAQsH,YAActH,EAAQuH,UAAYvH,EAAQwH,SAAWxH,EAAQyH,WAAazH,EAAQ0H,WAAa1H,EAAQwC,KAAOxC,EAAQ6G,KAAO7G,EAAQ6C,WAAa7C,EAAQ2H,IAAM3H,EAAQ4H,QAAK,EAGlV,MAAMC,EAAW,EAAQ,KAezB,GAZA7H,EAAQ4H,GADIE,GAAQ,IAAIhH,WAAWgH,EAAInF,OAAQmF,EAAIjE,WAAYiE,EAAIC,YAGnE/H,EAAQ2H,IADKG,GAAQ,IAAIlC,YAAYkC,EAAInF,OAAQmF,EAAIjE,WAAYT,KAAK4E,MAAMF,EAAIC,WAAa,IAI7F/H,EAAQ6C,WADYiF,GAAQ,IAAIG,SAASH,EAAInF,OAAQmF,EAAIjE,WAAYiE,EAAIC,YAIzE/H,EAAQ6G,KADK,CAACqB,EAAMC,IAAWD,GAAS,GAAKC,EAAWD,IAASC,EAEjEnI,EAAQwC,KAAmE,KAA5D,IAAI1B,WAAW,IAAI8E,YAAY,CAAC,YAAajD,QAAQ,IAG/D3C,EAAQwC,KACT,MAAM,IAAI/B,MAAM,+CACpB,MAAM2H,EAAQC,MAAMC,KAAK,CAAEtH,OAAQ,MAAO,CAAC0D,EAAGd,IAAMA,EAAE2E,SAAS,IAAIC,SAAS,EAAG,OAsD/E,SAASlB,EAAYmB,GACjB,GAAmB,iBAARA,EACP,MAAM,IAAI1H,UAAU,2CAA2C0H,GAEnE,OAAO,IAAIC,aAAcC,OAAOF,EACpC,CAEA,SAASvF,EAAQH,GAGb,GAFoB,iBAATA,IACPA,EAAOuE,EAAYvE,MACjBA,aAAgBjC,YAClB,MAAM,IAAIC,UAAU,iDAAiDgC,MACzE,OAAOA,CACX,CArDA/C,EAAQ0H,WAVR,SAAoBkB,GAEhB,KAAMA,aAAkB9H,YACpB,MAAM,IAAIL,MAAM,uBACpB,IAAIoI,EAAM,GACV,IAAK,IAAIjF,EAAI,EAAGA,EAAIgF,EAAO5H,OAAQ4C,IAC/BiF,GAAOT,EAAMQ,EAAOhF,IAExB,OAAOiF,CACX,EAsBA7I,EAAQyH,WAjBR,SAAoBoB,GAChB,GAAmB,iBAARA,EACP,MAAM,IAAI9H,UAAU,2CAA6C8H,GAErE,GAAIA,EAAI7H,OAAS,EACb,MAAM,IAAIP,MAAM,6CACpB,MAAMqI,EAAQ,IAAIhI,WAAW+H,EAAI7H,OAAS,GAC1C,IAAK,IAAI4C,EAAI,EAAGA,EAAIkF,EAAM9H,OAAQ4C,IAAK,CACnC,MAAMmF,EAAQ,EAAJnF,EACJoF,EAAUH,EAAIhE,MAAMkE,EAAGA,EAAI,GAC3BE,EAAO1I,OAAO2I,SAASF,EAAS,IACtC,GAAIzI,OAAO4I,MAAMF,IAASA,EAAO,EAC7B,MAAM,IAAIxI,MAAM,yBACpBqI,EAAMlF,GAAKqF,CACf,CACA,OAAOH,CACX,EAKA9I,EAAQwH,SADS4B,YAejBpJ,EAAQuH,UAZR6B,eAAyBC,EAAOC,EAAMC,GAClC,IAAIC,EAAKC,KAAKC,MACd,IAAK,IAAI9F,EAAI,EAAGA,EAAIyF,EAAOzF,IAAK,CAC5B2F,EAAG3F,GAEH,MAAM+F,EAAOF,KAAKC,MAAQF,EACtBG,GAAQ,GAAKA,EAAOL,UAElB,EAAItJ,EAAQwH,YAClBgC,GAAMG,EACV,CACJ,EAQA3J,EAAQsH,YAAcA,EAQtBtH,EAAQkD,QAAUA,EAmBlBlD,EAAQqH,YAdR,YAAwBuC,GACpB,IAAKA,EAAOC,OAAOpE,GAAMA,aAAa3E,aAClC,MAAM,IAAIL,MAAM,4BACpB,GAAsB,IAAlBmJ,EAAO5I,OACP,OAAO4I,EAAO,GAClB,MAAM5I,EAAS4I,EAAOE,QAAO,CAACrE,EAAGqC,IAAQrC,EAAIqC,EAAI9G,QAAQ,GACnD+I,EAAS,IAAIjJ,WAAWE,GAC9B,IAAK,IAAI4C,EAAI,EAAGoG,EAAM,EAAGpG,EAAIgG,EAAO5I,OAAQ4C,IAAK,CAC7C,MAAMkE,EAAM8B,EAAOhG,GACnBmG,EAAO1G,IAAIyE,EAAKkC,GAChBA,GAAOlC,EAAI9G,MACf,CACA,OAAO+I,CACX,EASA/J,EAAQqC,KANR,MAEI4H,QACI,OAAO7J,KAAK2E,YAChB,GAWJ/E,EAAQoH,UANR,SAAmB8C,EAAUC,GACzB,QAAahF,IAATgF,IAAuC,iBAATA,IAFfC,EAEmDD,EAFH,oBAAxCrI,OAAOuI,UAAU9B,SAAS+B,KAAKF,IAA8BA,EAAI9H,cAAgBR,SAGxG,MAAM,IAAIf,UAAU,yCAHN,IAACqJ,EAKnB,OADetI,OAAOyI,OAAOL,EAAUC,EAE3C,EAUAnK,EAAQiH,gBARR,SAAyBuD,GACrB,MAAMC,EAASC,GAAYF,IAAkB1H,OAAOI,EAAQwH,IAAU/F,SAChEgG,EAAMH,IAIZ,OAHAC,EAAMrJ,UAAYuJ,EAAIvJ,UACtBqJ,EAAMpJ,SAAWsJ,EAAItJ,SACrBoJ,EAAMtJ,OAAS,IAAMqJ,IACdC,CACX,EAUAzK,EAAQmH,wBARR,SAAiCyD,GAC7B,MAAMH,EAAQ,CAACI,EAAKV,IAASS,EAAST,GAAMrH,OAAOI,EAAQ2H,IAAMlG,SAC3DgG,EAAMC,EAAS,CAAC,GAItB,OAHAH,EAAMrJ,UAAYuJ,EAAIvJ,UACtBqJ,EAAMpJ,SAAWsJ,EAAItJ,SACrBoJ,EAAMtJ,OAAUgJ,GAASS,EAAST,GAC3BM,CACX,EAgBAzK,EAAQkH,YAXR,SAAqB4D,EAAc,IAC/B,GAAIjD,EAAS5C,OAAOG,IAChB,OAAOyC,EAAS5C,OAAOG,IAAI2F,gBAAgB,IAAIjK,WAAWgK,IAEzD,GAAIjD,EAAS5C,OAAOC,KACrB,OAAO,IAAIpE,WAAW+G,EAAS5C,OAAOC,KAAKgC,YAAY4D,GAAanI,QAGpE,MAAM,IAAIlC,MAAM,oDAExB,WCvCAR,EAAOD,QAlHP,SAAegL,GACb,GAAIA,EAAShK,QAAU,IAAO,MAAM,IAAID,UAAU,qBAElD,IADA,IAAIkK,EAAW,IAAInK,WAAW,KACrBiI,EAAI,EAAGA,EAAIkC,EAASjK,OAAQ+H,IACnCkC,EAASlC,GAAK,IAEhB,IAAK,IAAInF,EAAI,EAAGA,EAAIoH,EAAShK,OAAQ4C,IAAK,CACxC,IAAIsH,EAAIF,EAASG,OAAOvH,GACpBwH,EAAKF,EAAEG,WAAW,GACtB,GAAqB,MAAjBJ,EAASG,GAAe,MAAM,IAAIrK,UAAUmK,EAAI,iBACpDD,EAASG,GAAMxH,CACjB,CACA,IAAI0H,EAAON,EAAShK,OAChBuK,EAASP,EAASG,OAAO,GACzBK,EAASpI,KAAKqI,IAAIH,GAAQlI,KAAKqI,IAAI,KACnCC,EAAUtI,KAAKqI,IAAI,KAAOrI,KAAKqI,IAAIH,GA8CvC,SAASK,EAAcC,GACrB,GAAsB,iBAAXA,EAAuB,MAAM,IAAI7K,UAAU,mBACtD,GAAsB,IAAlB6K,EAAO5K,OAAgB,OAAO,IAAIF,WAKtC,IAJA,IAAI+K,EAAM,EAENC,EAAS,EACT9K,EAAS,EACN4K,EAAOC,KAASN,GACrBO,IACAD,IAMF,IAHA,IAAIE,GAAUH,EAAO5K,OAAS6K,GAAOL,EAAU,IAAO,EAClDQ,EAAO,IAAIlL,WAAWiL,GAEnBH,EAAOC,IAAM,CAElB,IAAII,EAAQhB,EAASW,EAAOP,WAAWQ,IAEvC,GAAc,MAAVI,EAAiB,OAErB,IADA,IAAIrI,EAAI,EACCsI,EAAMH,EAAO,GAAc,IAAVE,GAAerI,EAAI5C,KAAqB,IAATkL,EAAaA,IAAOtI,IAC3EqI,GAAUX,EAAOU,EAAKE,KAAU,EAChCF,EAAKE,GAAQD,EAAQ,MAAS,EAC9BA,EAASA,EAAQ,MAAS,EAE5B,GAAc,IAAVA,EAAe,MAAM,IAAIxL,MAAM,kBACnCO,EAAS4C,EACTiI,GACF,CAGA,IADA,IAAIM,EAAMJ,EAAO/K,EACVmL,IAAQJ,GAAsB,IAAdC,EAAKG,IAC1BA,IAIF,IAFA,IAAIC,EAAM,IAAItL,WAAWgL,GAAUC,EAAOI,IACtCpD,EAAI+C,EACDK,IAAQJ,GACbK,EAAIrD,KAAOiD,EAAKG,KAElB,OAAOC,CACT,CAMA,MAAO,CACLzD,OA7FF,SAAiBiD,GAOf,GANIA,aAAkB9K,aACXuL,YAAYC,OAAOV,GAC5BA,EAAS,IAAI9K,WAAW8K,EAAOjJ,OAAQiJ,EAAO/H,WAAY+H,EAAO7D,YACxDM,MAAMkE,QAAQX,KACvBA,EAAS9K,WAAWwH,KAAKsD,OAErBA,aAAkB9K,YAAe,MAAM,IAAIC,UAAU,uBAC3D,GAAsB,IAAlB6K,EAAO5K,OAAgB,MAAO,GAMlC,IAJA,IAAI8K,EAAS,EACT9K,EAAS,EACTwL,EAAS,EACTC,EAAOb,EAAO5K,OACXwL,IAAWC,GAA2B,IAAnBb,EAAOY,IAC/BA,IACAV,IAMF,IAHA,IAAIC,GAASU,EAAOD,GAAUd,EAAU,IAAO,EAC3CgB,EAAM,IAAI5L,WAAWiL,GAElBS,IAAWC,GAAM,CAItB,IAHA,IAAIR,EAAQL,EAAOY,GAEf5I,EAAI,EACC+I,EAAMZ,EAAO,GAAc,IAAVE,GAAerI,EAAI5C,KAAqB,IAAT2L,EAAaA,IAAO/I,IAC3EqI,GAAU,IAAMS,EAAIC,KAAU,EAC9BD,EAAIC,GAAQV,EAAQX,IAAU,EAC9BW,EAASA,EAAQX,IAAU,EAE7B,GAAc,IAAVW,EAAe,MAAM,IAAIxL,MAAM,kBACnCO,EAAS4C,EACT4I,GACF,CAGA,IADA,IAAII,EAAMb,EAAO/K,EACV4L,IAAQb,GAAqB,IAAbW,EAAIE,IACzBA,IAIF,IADA,IAAInE,EAAM8C,EAAOsB,OAAOf,GACjBc,EAAMb,IAAQa,EAAOnE,GAAOuC,EAASG,OAAOuB,EAAIE,IACvD,OAAOnE,CACT,EAkDEkD,aAAcA,EACdmB,OARF,SAAiBC,GACf,IAAIpK,EAASgJ,EAAaoB,GAC1B,GAAIpK,EAAU,OAAOA,EACrB,MAAM,IAAIlC,MAAM,WAAa6K,EAAO,aACtC,EAMF,iBCtHAxJ,OAAOC,eAAe/B,EAAS,aAAc,CAAEgC,OAAO,IACtDhC,EAAQgN,SAAWhN,EAAQiN,SAAWjN,EAAQkN,iBAAmBlN,EAAQmN,WAAanN,EAAQoN,cAAW,EACzG,MAAMC,EAAa,EAAQ,KACrBC,EAAc,EAAQ,IACtBC,EAAU,EAAQ,IACxBvN,EAAQoN,SAAW,CACfI,QAAS,CACLC,MAAO,GACPC,KAAM,IAEVC,QAAS,CACLF,MAAO,GACPC,KAAM,KAId,MAAME,EAAyB,CAAC,EAChCA,EAAuB,GAAK5N,EAAQoN,SAASI,QAAQC,MACrDG,EAAuB,GAAK5N,EAAQoN,SAASI,QAAQE,KACrDE,EAAuB,KAAO5N,EAAQoN,SAASO,QAAQF,MACvDG,EAAuB,KAAO5N,EAAQoN,SAASO,QAAQD,KAEvD,MAAMG,EAAyB,CAAC,EAahC,SAASV,EAAWW,EAASC,GACzB,IAAKA,EAAWC,MAAM,qBAClB,MAAM,IAAIvN,MAAM,8CAGpB,MAAO,KADW,EAAI4M,EAAWY,gBAAgBH,EAASC,IAE9D,CAOA,SAASb,EAAiBgB,GACtB,GAAIA,EAAQlN,QAAU,EAClB,MAAM,IAAIP,MAAM,uCAEpB,GAAkB,KAAdyN,EAAQ,GACR,MAAM,IAAIzN,MAAM,4CAEpB,OAAO,EAAI4M,EAAWc,gBAAgBD,EAAQrJ,MAAM,GACxD,CAjCAgJ,EAAuB7N,EAAQoN,SAASI,QAAQC,OAAS,EACzDI,EAAuB7N,EAAQoN,SAASI,QAAQE,MAAQ,EACxDG,EAAuB7N,EAAQoN,SAASO,QAAQF,OAAS,IACzDI,EAAuB7N,EAAQoN,SAASO,QAAQD,MAAQ,IAgBxD1N,EAAQmN,WAAaA,EAerBnN,EAAQkN,iBAAmBA,EAyB3BlN,EAAQiN,SAhBR,SAAkBmB,EAAUN,GAAU,GAClC,MAAMO,EAAWf,EAAYR,OAAOsB,GAC9BE,GAAgB,EAAIf,EAAQ7F,YAAY2G,EAAStL,MACjDwL,EAAcrF,UAAS,EAAIqE,EAAQ7F,YAAY2G,EAASG,QAAS,IACvE,IAAIC,EAUJ,OATIX,EAAU,GACVW,EAAgBF,OAC4BpJ,IAAxCyI,EAAuBW,KACvBE,EAAgBb,EAAuBW,KAI3CE,EAAgBX,EAEbX,EAAWsB,EAAeH,EACrC,EA6BAtO,EAAQgN,SApBR,SAAkB0B,EAAWZ,GAAU,GACnC,MAAMO,EAAWnB,EAAiBwB,GAC5BD,EAAgBJ,EAAS,GACzBC,EAAgBD,EAAS,GAC/B,IAAIM,EACAb,EAAU,GACVa,EAAiBF,OAC6BtJ,IAA1C0I,EAAuBY,KACvBE,EAAiBd,EAAuBY,KAI5CE,EAAiBb,EAErB,IAAIU,EAASG,EAAepG,SAAS,IAIrC,OAHsB,IAAlBiG,EAAOxN,SACPwN,EAAS,IAAIA,KAEVlB,EAAY3E,OAAO2F,EAAeE,EAC7C,gBCxGA1M,OAAOC,eAAe/B,EAAS,aAAc,CAAEgC,OAAO,IACtDhC,EAAQ8M,OAAS9M,EAAQ2I,YAAS,EAClC,MAAMiG,EAAW,EAAQ,IACnBrB,EAAU,EAAQ,IAClBsB,EAAQ,EAAQ,KAChB7D,EAAW,6DAUjBhL,EAAQ2I,OATR,SAAgB5F,EAAMyL,EAAS,MAC3B,MAAMM,EAA4B,iBAAT/L,GAAoB,EAAIwK,EAAQ9F,YAAY1E,GAAQA,EACvEgM,EAAgC,iBAAXP,GAAsB,EAAIjB,EAAQ9F,YAAY+G,GAAUzL,EACnF,KAAM+L,aAAqBhO,YAAiBiO,aAAuBjO,YAC/D,MAAM,IAAIC,UAAU,iDAExB,MAAMiO,GAAW,EAAIJ,EAAStJ,SAAQ,EAAIsJ,EAAStJ,QAAQ,IAAIxE,WAAW,IAAIiO,KAAgBD,MAC9F,OAAOD,EAAM7D,GAAUrC,OAAO,IAAIoG,KAAgBD,KAAcE,EAASnK,MAAM,EAAG,IACtF,EAeA7E,EAAQ8M,OAbR,SAAgBC,GACZ,MAAMnM,EAAQiO,EAAM7D,GAAU8B,OAAOC,GAC/BgC,EAAcnO,EAAMiE,MAAM,EAAG,GAC7BiK,EAAYlO,EAAMiE,MAAM,GAAI,GAE5BmK,GAAW,EAAIJ,EAAStJ,SAAQ,EAAIsJ,EAAStJ,QAAQ,IAAIxE,WAAW,IAAIiO,KAAgBD,MAM9F,OALAlO,EAAMiE,OAAO,GAAGJ,SAAQ,CAACwK,EAAOC,KAC5B,GAAID,IAAUD,EAASE,GACnB,MAAM,IAAIzO,MAAM,mBACpB,IAEG,CAAE+N,OAAQO,EAAahM,KAAM+L,EACxC,iBCjCAhN,OAAOC,eAAe/B,EAAS,aAAc,CAAEgC,OAAO,IACtDhC,EAAQmO,eAAiBnO,EAAQiO,oBAAiB,EAClD,MAAMW,EAAW,EAAQ,IACnBrB,EAAU,EAAQ,IAClB4B,EAAa,EAAQ,KAM3B,SAASC,EAAYC,GACjB,MAAMC,GAAW,EAAIV,EAAStJ,SAAQ,EAAIsJ,EAAStJ,SAAQ,EAAIiI,EAAQ9F,YAAY4H,KAEnF,OADiB,EAAI9B,EAAQ7F,YAAY4H,EAASzK,MAAM,EAAG,GAE/D,CA+BA7E,EAAQiO,eAnBR,SAAwBH,EAAS/K,GAC7B,GAAI+K,EAAU,GAAKA,GAAW,GAC1B,MAAM,IAAIrN,MAAM,8CAEpB,IAAKsC,EAAKiL,MAAM,kBACZ,MAAM,IAAIvN,MAAM,oCAEpBsC,EAAOA,EAAKwM,eACHvO,OAAS,GAAM,IACpB+B,EAAO,IAAIA,KAEf,IAAIyM,EAAa1B,EAAQvF,SAAS,IACR,IAAtBiH,EAAWxO,SACXwO,EAAa,IAAIA,KAErB,MAAMC,EAAcL,EAAY,GAAGI,IAAazM,KAC1C2M,GAAS,EAAIP,EAAWQ,WAAW,GAAG5M,IAAO0M,KACnD,MAAO,GAAGN,EAAWS,IAAI9B,KAAW4B,GACxC,EA6BA1P,EAAQmO,eAfR,SAAwB0B,GACpBA,GAAU,EAAIV,EAAWW,cAAcD,GACvC,MAAMR,GAAU,EAAIF,EAAWY,WAAWF,EAAQhL,MAAM,IAClDmL,EAAcH,EAAQ,GACtB/B,EAAUqB,EAAWS,IAAIK,QAAQD,GACjChB,EAAWK,EAAQxK,OAAO,GAChC,IAAI2K,EAAa1B,EAAQvF,SAAS,IAIlC,GAH0B,IAAtBiH,EAAWxO,SACXwO,EAAa,IAAIA,KAEjBJ,EAAY,GAAGI,IAAaH,EAAQa,UAAU,EAAGb,EAAQrO,OAAS,QAAUgO,EAC5E,MAAM,IAAIvO,MAAM,8CAEpB,MAAO,CAACqN,EAASuB,EAAQa,UAAU,EAAGb,EAAQrO,OAAS,GAC3D,iBCxEAc,OAAOC,eAAe/B,EAAS,aAAc,CAAEgC,OAAO,IACtDhC,EAAQ+P,UAAY/P,EAAQ8P,aAAe9P,EAAQ2P,UAAY3P,EAAQ4P,SAAM,EAC7E,MAAMrC,EAAU,EAAQ,IACxBvN,EAAQ4P,IAAM,mCACd,MAAM/G,EAAM,mBAkEZ,SAASiH,EAAaK,GAIlB,OAAOA,EAASC,cAAcC,QAAQ,KAAM,KAAKA,QAAQ,OAAQ,IACrE,CAXArQ,EAAQ2P,UApDR,SAAmBW,EAAUC,GAEzB,IAAKD,EAAStC,MAAM,kBAChB,MAAM,IAAIvN,MAAM,4BAEhB6P,EAAStP,OAAS,GAAM,IACxBsP,EAAW,IAAIA,KAGnB,IAAI1L,EAAM,GACNqH,EAAQ,EACZ,IAAK,IAAIrI,GAHT0M,EAAWA,EAASf,eAGEvO,OAAS,EAAG4C,GAAK,EAAGA,IACtC,GAAIqI,EAAQ,EAAG,CACX,MAAMuE,EAAc3H,EAAIoH,QAAQK,EAAS1M,KAAOqI,EAChD,IAAIwE,EAAW,EACL,IAAN7M,IACA6M,EAAW5H,EAAIoH,QAAQK,EAAS1M,EAAI,KAGxC,MAAM8M,EAAW,EAAIzE,EACf0E,EAAcF,GAAY,GAAKC,IAAc,EAAIA,EACjDE,EAAc5Q,EAAQ4P,IAAIY,EAAcG,GAC9C1E,EAAQyE,EACR9L,EAAIiM,QAAQD,EAChB,MAEI3E,EAAQ,EAGhB,IAAI6E,EAAkB,EACtB,IAAK,IAAIlN,EAAI,EAAGA,EAAIgB,EAAI5D,QACL,MAAX4D,EAAIhB,GADoBA,IAKxBkN,IAGRlM,EAAMA,EAAIC,MAAMiM,GAChB,MAAMC,GAAa,IAAIC,aAAclE,QAAO,EAAIS,EAAQ9F,YAAY6I,IAAWtC,MAAM,YAC/EiD,EAA2BF,EAAaA,EAAW,GAAG/P,OAAS,EACrE,IAAK,IAAI4C,EAAI,EAAGA,EAAIqN,EAA0BrN,IAC1CgB,EAAIiM,QAAQ7Q,EAAQ4P,IAAI,IAE5B,GAAIW,EAAW,CACX,MAAMW,EAAQX,EAAY3L,EAAI5D,OAC9B,IAAK,IAAI4C,EAAI,EAAGA,EAAIsN,EAAOtN,IACvBgB,EAAIiM,QAAQ7Q,EAAQ4P,IAAI,GAEhC,CACA,OAAOhL,EAAIuM,KAAK,GACpB,EAaAnR,EAAQ8P,aAAeA,EA+DvB9P,EAAQ+P,UAtDR,SAAmBI,EAAUI,GAGzB,KAFAJ,EAAWL,EAAaK,IAEVnC,MAAM,KAAKhO,EAAQ4P,UAC7B,MAAM,IAAInP,MAAM,4BAEpB,MAAMsQ,EAAaZ,EAASnC,MAAM,IAAIhO,EAAQ4P,IAAI,OAC5CwB,EAAsBL,EAAaA,EAAW,GAAG/P,OAAS,EAChE,IAAI4D,EAAM,GACNqH,EAAQ,EACRoF,EAAY,EAChB,IAAK,IAAIzN,EAAIuM,EAASnP,OAAS,EAAG4C,GAAK,EAAGA,IAAK,CACzB,IAAdyN,IACAzM,EAAIiM,QAAQhI,EAAIoD,IAChBoF,EAAY,EACZpF,EAAQ,GAEZ,MACMqF,GADctR,EAAQ4P,IAAIK,QAAQE,EAASvM,KAAOyN,GACrBpF,EAC7BsF,EAAkB1I,EAAIyI,EAAe,IAG3C,GAFAD,GAAa,EACbpF,EAAQqF,GAAgB,EACpBrF,EAAQ,GAAKoF,EACb,MAAM,IAAI5Q,MAAM,4BAEpBmE,EAAIiM,QAAQU,EAChB,CAEA3M,EAAIiM,QAAQhI,EAAIoD,IACZrH,EAAI5D,OAAS,GAAM,GACnB4D,EAAIiM,QAAQ,KAEhB,IAAIW,EAAkB,EACtB,IAAK,IAAI5N,EAAI,EAAGA,EAAIgB,EAAI5D,QACL,MAAX4D,EAAIhB,GADoBA,IAKxB4N,IAGR5M,EAAMA,EAAIC,MAAM2M,EAAmBA,EAAkB,GACrD,IAAIC,EAAS7M,EAAIuM,KAAK,IACtB,IAAK,IAAIvN,EAAI,EAAGA,EAAIwN,EAAqBxN,IACrC6N,EAAS,KAAKA,IAElB,GAAIlB,EAAW,CACX,MAAMW,EAAoB,EAAZX,EAAgBkB,EAAOzQ,OACrC,IAAK,IAAI4C,EAAI,EAAGA,EAAIsN,EAAOtN,GAAK,EAC5B6N,EAAS,KAAKA,GAEtB,CACA,OAAOA,CACX,IC1IIC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBzM,IAAjB0M,EACH,OAAOA,EAAa7R,QAGrB,IAAIC,EAASyR,EAAyBE,GAAY,CAGjD5R,QAAS,CAAC,GAOX,OAHA8R,EAAoBF,GAAU3R,EAAQA,EAAOD,QAAS2R,GAG/C1R,EAAOD,OACf,8BCrBA8B,OAAOC,eAAe/B,EAAS,aAAc,CAAEgC,OAAO,IACtDhC,EAAQiN,SAAWjN,EAAQgN,SAAWhN,EAAQoN,SAAWpN,EAAQ8P,aAAe9P,EAAQkN,iBAAmBlN,EAAQmN,WAAanN,EAAQmO,eAAiBnO,EAAQiO,eAAiBjO,EAAQ+P,UAAY/P,EAAQ2P,eAAY,EAC1N,MAAMR,EAAa,EAAQ,KAC3BrN,OAAOC,eAAe/B,EAAS,YAAa,CAAE+R,YAAY,EAAMvN,IAAK,WAAc,OAAO2K,EAAWQ,SAAW,IAChH7N,OAAOC,eAAe/B,EAAS,YAAa,CAAE+R,YAAY,EAAMvN,IAAK,WAAc,OAAO2K,EAAWY,SAAW,IAChHjO,OAAOC,eAAe/B,EAAS,eAAgB,CAAE+R,YAAY,EAAMvN,IAAK,WAAc,OAAO2K,EAAWW,YAAc,IACtH,MAAMzC,EAAa,EAAQ,KAC3BvL,OAAOC,eAAe/B,EAAS,iBAAkB,CAAE+R,YAAY,EAAMvN,IAAK,WAAc,OAAO6I,EAAWY,cAAgB,IAC1HnM,OAAOC,eAAe/B,EAAS,iBAAkB,CAAE+R,YAAY,EAAMvN,IAAK,WAAc,OAAO6I,EAAWc,cAAgB,IAC1H,MAAM6D,EAAY,EAAQ,KAC1BlQ,OAAOC,eAAe/B,EAAS,aAAc,CAAE+R,YAAY,EAAMvN,IAAK,WAAc,OAAOwN,EAAU7E,UAAY,IACjHrL,OAAOC,eAAe/B,EAAS,mBAAoB,CAAE+R,YAAY,EAAMvN,IAAK,WAAc,OAAOwN,EAAU9E,gBAAkB,IAC7HpL,OAAOC,eAAe/B,EAAS,WAAY,CAAE+R,YAAY,EAAMvN,IAAK,WAAc,OAAOwN,EAAUhF,QAAU,IAC7GlL,OAAOC,eAAe/B,EAAS,WAAY,CAAE+R,YAAY,EAAMvN,IAAK,WAAc,OAAOwN,EAAU/E,QAAU,IAC7GnL,OAAOC,eAAe/B,EAAS,WAAY,CAAE+R,YAAY,EAAMvN,IAAK,WAAc,OAAOwN,EAAU5E,QAAU", "sources": ["webpack://c32check/webpack/universalModuleDefinition", "webpack://c32check/./node_modules/@noble/hashes/_assert.js", "webpack://c32check/./node_modules/@noble/hashes/_sha2.js", "webpack://c32check/./node_modules/@noble/hashes/cryptoBrowser.js", "webpack://c32check/./node_modules/@noble/hashes/sha256.js", "webpack://c32check/./node_modules/@noble/hashes/utils.js", "webpack://c32check/./node_modules/base-x/src/index.js", "webpack://c32check/./src/address.ts", "webpack://c32check/./src/base58check.ts", "webpack://c32check/./src/checksum.ts", "webpack://c32check/./src/encoding.ts", "webpack://c32check/webpack/bootstrap", "webpack://c32check/./src/index.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"c32check\"] = factory();\n\telse\n\t\troot[\"c32check\"] = factory();\n})(this, () => {\nreturn ", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.output = exports.exists = exports.hash = exports.bytes = exports.bool = exports.number = void 0;\nfunction number(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error(`Wrong positive integer: ${n}`);\n}\nexports.number = number;\nfunction bool(b) {\n    if (typeof b !== 'boolean')\n        throw new Error(`Expected boolean, not ${b}`);\n}\nexports.bool = bool;\nfunction bytes(b, ...lengths) {\n    if (!(b instanceof Uint8Array))\n        throw new TypeError('Expected Uint8Array');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new TypeError(`Expected Uint8Array of length ${lengths}, not of length=${b.length}`);\n}\nexports.bytes = bytes;\nfunction hash(hash) {\n    if (typeof hash !== 'function' || typeof hash.create !== 'function')\n        throw new Error('Hash should be wrapped by utils.wrapConstructor');\n    number(hash.outputLen);\n    number(hash.blockLen);\n}\nexports.hash = hash;\nfunction exists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\nexports.exists = exists;\nfunction output(out, instance) {\n    bytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error(`digestInto() expects output buffer of length at least ${min}`);\n    }\n}\nexports.output = output;\nconst assert = {\n    number,\n    bool,\n    bytes,\n    hash,\n    exists,\n    output,\n};\nexports.default = assert;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.SHA2 = void 0;\nconst _assert_js_1 = require(\"./_assert.js\");\nconst utils_js_1 = require(\"./utils.js\");\n// Polyfill for Safari 14\nfunction setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n// Base SHA2 class (RFC 6234)\nclass SHA2 extends utils_js_1.Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = (0, utils_js_1.createView)(this.buffer);\n    }\n    update(data) {\n        _assert_js_1.default.exists(this);\n        const { view, buffer, blockLen } = this;\n        data = (0, utils_js_1.toBytes)(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = (0, utils_js_1.createView)(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        _assert_js_1.default.exists(this);\n        _assert_js_1.default.output(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        this.buffer.subarray(pos).fill(0);\n        // we have less than padOffset left in buffer, so we cannot put length in current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = (0, utils_js_1.createView)(out);\n        this.get().forEach((v, i) => oview.setUint32(4 * i, v, isLE));\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.length = length;\n        to.pos = pos;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n}\nexports.SHA2 = SHA2;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.crypto = void 0;\nexports.crypto = {\n    node: undefined,\n    web: typeof self === 'object' && 'crypto' in self ? self.crypto : undefined,\n};\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sha256 = void 0;\nconst _sha2_js_1 = require(\"./_sha2.js\");\nconst utils_js_1 = require(\"./utils.js\");\n// Choice: a ? b : c\nconst Chi = (a, b, c) => (a & b) ^ (~a & c);\n// Majority function, true if any two inpust is true\nconst Maj = (a, b, c) => (a & b) ^ (a & c) ^ (b & c);\n// Round constants:\n// first 32 bits of the fractional parts of the cube roots of the first 64 primes 2..311)\n// prettier-ignore\nconst SHA256_K = new Uint32Array([\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n// Initial state (first 32 bits of the fractional parts of the square roots of the first 8 primes 2..19):\n// prettier-ignore\nconst IV = new Uint32Array([\n    0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19\n]);\n// Temporary buffer, not used to store anything between runs\n// Named this way because it matches specification.\nconst SHA256_W = new Uint32Array(64);\nclass SHA256 extends _sha2_js_1.SHA2 {\n    constructor() {\n        super(64, 32, 8, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        this.A = IV[0] | 0;\n        this.B = IV[1] | 0;\n        this.C = IV[2] | 0;\n        this.D = IV[3] | 0;\n        this.E = IV[4] | 0;\n        this.F = IV[5] | 0;\n        this.G = IV[6] | 0;\n        this.H = IV[7] | 0;\n    }\n    get() {\n        const { A, B, C, D, E, F, G, H } = this;\n        return [A, B, C, D, E, F, G, H];\n    }\n    // prettier-ignore\n    set(A, B, C, D, E, F, G, H) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n        this.F = F | 0;\n        this.G = G | 0;\n        this.H = H | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA256_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 64; i++) {\n            const W15 = SHA256_W[i - 15];\n            const W2 = SHA256_W[i - 2];\n            const s0 = (0, utils_js_1.rotr)(W15, 7) ^ (0, utils_js_1.rotr)(W15, 18) ^ (W15 >>> 3);\n            const s1 = (0, utils_js_1.rotr)(W2, 17) ^ (0, utils_js_1.rotr)(W2, 19) ^ (W2 >>> 10);\n            SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n        }\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D, E, F, G, H } = this;\n        for (let i = 0; i < 64; i++) {\n            const sigma1 = (0, utils_js_1.rotr)(E, 6) ^ (0, utils_js_1.rotr)(E, 11) ^ (0, utils_js_1.rotr)(E, 25);\n            const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const sigma0 = (0, utils_js_1.rotr)(A, 2) ^ (0, utils_js_1.rotr)(A, 13) ^ (0, utils_js_1.rotr)(A, 22);\n            const T2 = (sigma0 + Maj(A, B, C)) | 0;\n            H = G;\n            G = F;\n            F = E;\n            E = (D + T1) | 0;\n            D = C;\n            C = B;\n            B = A;\n            A = (T1 + T2) | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        F = (F + this.F) | 0;\n        G = (G + this.G) | 0;\n        H = (H + this.H) | 0;\n        this.set(A, B, C, D, E, F, G, H);\n    }\n    roundClean() {\n        SHA256_W.fill(0);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n        this.buffer.fill(0);\n    }\n}\n/**\n * SHA2-256 hash function\n * @param message - data that would be hashed\n */\nexports.sha256 = (0, utils_js_1.wrapConstructor)(() => new SHA256());\n", "\"use strict\";\n/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.randomBytes = exports.wrapConstructorWithOpts = exports.wrapConstructor = exports.checkOpts = exports.Hash = exports.concatBytes = exports.toBytes = exports.utf8ToBytes = exports.asyncLoop = exports.nextTick = exports.hexToBytes = exports.bytesToHex = exports.isLE = exports.rotr = exports.createView = exports.u32 = exports.u8 = void 0;\n// The import here is via the package name. This is to ensure\n// that exports mapping/resolution does fall into place.\nconst crypto_1 = require(\"@noble/hashes/crypto\");\n// Cast array to different type\nconst u8 = (arr) => new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\nexports.u8 = u8;\nconst u32 = (arr) => new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\nexports.u32 = u32;\n// Cast array to view\nconst createView = (arr) => new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\nexports.createView = createView;\n// The rotate right (circular right shift) operation for uint32\nconst rotr = (word, shift) => (word << (32 - shift)) | (word >>> shift);\nexports.rotr = rotr;\nexports.isLE = new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44;\n// There is almost no big endian hardware, but js typed arrays uses platform specific endianness.\n// So, just to be sure not to corrupt anything.\nif (!exports.isLE)\n    throw new Error('Non little-endian hardware is not supported');\nconst hexes = Array.from({ length: 256 }, (v, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xde, 0xad, 0xbe, 0xef]))\n */\nfunction bytesToHex(uint8a) {\n    // pre-caching improves the speed 6x\n    if (!(uint8a instanceof Uint8Array))\n        throw new Error('Uint8Array expected');\n    let hex = '';\n    for (let i = 0; i < uint8a.length; i++) {\n        hex += hexes[uint8a[i]];\n    }\n    return hex;\n}\nexports.bytesToHex = bytesToHex;\n/**\n * @example hexToBytes('deadbeef')\n */\nfunction hexToBytes(hex) {\n    if (typeof hex !== 'string') {\n        throw new TypeError('hexToBytes: expected string, got ' + typeof hex);\n    }\n    if (hex.length % 2)\n        throw new Error('hexToBytes: received invalid unpadded hex');\n    const array = new Uint8Array(hex.length / 2);\n    for (let i = 0; i < array.length; i++) {\n        const j = i * 2;\n        const hexByte = hex.slice(j, j + 2);\n        const byte = Number.parseInt(hexByte, 16);\n        if (Number.isNaN(byte) || byte < 0)\n            throw new Error('Invalid byte sequence');\n        array[i] = byte;\n    }\n    return array;\n}\nexports.hexToBytes = hexToBytes;\n// There is no setImmediate in browser and setTimeout is slow. However, call to async function will return Promise\n// which will be fullfiled only on next scheduler queue processing step and this is exactly what we need.\nconst nextTick = async () => { };\nexports.nextTick = nextTick;\n// Returns control to thread each 'tick' ms to avoid blocking\nasync function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for (let i = 0; i < iters; i++) {\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick)\n            continue;\n        await (0, exports.nextTick)();\n        ts += diff;\n    }\n}\nexports.asyncLoop = asyncLoop;\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string') {\n        throw new TypeError(`utf8ToBytes expected string, got ${typeof str}`);\n    }\n    return new TextEncoder().encode(str);\n}\nexports.utf8ToBytes = utf8ToBytes;\nfunction toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    if (!(data instanceof Uint8Array))\n        throw new TypeError(`Expected input type is Uint8Array (got ${typeof data})`);\n    return data;\n}\nexports.toBytes = toBytes;\n/**\n * Concats Uint8Array-s into one; like `Buffer.concat([buf1, buf2])`\n * @example concatBytes(buf1, buf2)\n */\nfunction concatBytes(...arrays) {\n    if (!arrays.every((a) => a instanceof Uint8Array))\n        throw new Error('Uint8Array list expected');\n    if (arrays.length === 1)\n        return arrays[0];\n    const length = arrays.reduce((a, arr) => a + arr.length, 0);\n    const result = new Uint8Array(length);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const arr = arrays[i];\n        result.set(arr, pad);\n        pad += arr.length;\n    }\n    return result;\n}\nexports.concatBytes = concatBytes;\n// For runtime check if class implements interface\nclass Hash {\n    // Safe version that clones internal state\n    clone() {\n        return this._cloneInto();\n    }\n}\nexports.Hash = Hash;\n// Check if object doens't have custom constructor (like Uint8Array/Array)\nconst isPlainObject = (obj) => Object.prototype.toString.call(obj) === '[object Object]' && obj.constructor === Object;\nfunction checkOpts(defaults, opts) {\n    if (opts !== undefined && (typeof opts !== 'object' || !isPlainObject(opts)))\n        throw new TypeError('Options should be object or undefined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\nexports.checkOpts = checkOpts;\nfunction wrapConstructor(hashConstructor) {\n    const hashC = (message) => hashConstructor().update(toBytes(message)).digest();\n    const tmp = hashConstructor();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashConstructor();\n    return hashC;\n}\nexports.wrapConstructor = wrapConstructor;\nfunction wrapConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nexports.wrapConstructorWithOpts = wrapConstructorWithOpts;\n/**\n * Secure PRNG\n */\nfunction randomBytes(bytesLength = 32) {\n    if (crypto_1.crypto.web) {\n        return crypto_1.crypto.web.getRandomValues(new Uint8Array(bytesLength));\n    }\n    else if (crypto_1.crypto.node) {\n        return new Uint8Array(crypto_1.crypto.node.randomBytes(bytesLength).buffer);\n    }\n    else {\n        throw new Error(\"The environment doesn't have randomBytes function\");\n    }\n}\nexports.randomBytes = randomBytes;\n", "'use strict'\n// base-x encoding / decoding\n// Copyright (c) 2018 base-x contributors\n// Copyright (c) 2014-2018 The Bitcoin Core developers (base58.cpp)\n// Distributed under the MIT software license, see the accompanying\n// file LICENSE or http://www.opensource.org/licenses/mit-license.php.\nfunction base (ALPHABET) {\n  if (ALPHABET.length >= 255) { throw new TypeError('Alphabet too long') }\n  var BASE_MAP = new Uint8Array(256)\n  for (var j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255\n  }\n  for (var i = 0; i < ALPHABET.length; i++) {\n    var x = ALPHABET.charAt(i)\n    var xc = x.charCodeAt(0)\n    if (BASE_MAP[xc] !== 255) { throw new TypeError(x + ' is ambiguous') }\n    BASE_MAP[xc] = i\n  }\n  var BASE = ALPHABET.length\n  var LEADER = ALPHABET.charAt(0)\n  var FACTOR = Math.log(BASE) / Math.log(256) // log(BASE) / log(256), rounded up\n  var iFACTOR = Math.log(256) / Math.log(BASE) // log(256) / log(BASE), rounded up\n  function encode (source) {\n    if (source instanceof Uint8Array) {\n    } else if (ArrayBuffer.isView(source)) {\n      source = new Uint8Array(source.buffer, source.byteOffset, source.byteLength)\n    } else if (Array.isArray(source)) {\n      source = Uint8Array.from(source)\n    }\n    if (!(source instanceof Uint8Array)) { throw new TypeError('Expected Uint8Array') }\n    if (source.length === 0) { return '' }\n        // Skip & count leading zeroes.\n    var zeroes = 0\n    var length = 0\n    var pbegin = 0\n    var pend = source.length\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++\n      zeroes++\n    }\n        // Allocate enough space in big-endian base58 representation.\n    var size = ((pend - pbegin) * iFACTOR + 1) >>> 0\n    var b58 = new Uint8Array(size)\n        // Process the bytes.\n    while (pbegin !== pend) {\n      var carry = source[pbegin]\n            // Apply \"b58 = b58 * 256 + ch\".\n      var i = 0\n      for (var it1 = size - 1; (carry !== 0 || i < length) && (it1 !== -1); it1--, i++) {\n        carry += (256 * b58[it1]) >>> 0\n        b58[it1] = (carry % BASE) >>> 0\n        carry = (carry / BASE) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      pbegin++\n    }\n        // Skip leading zeroes in base58 result.\n    var it2 = size - length\n    while (it2 !== size && b58[it2] === 0) {\n      it2++\n    }\n        // Translate the result into a string.\n    var str = LEADER.repeat(zeroes)\n    for (; it2 < size; ++it2) { str += ALPHABET.charAt(b58[it2]) }\n    return str\n  }\n  function decodeUnsafe (source) {\n    if (typeof source !== 'string') { throw new TypeError('Expected String') }\n    if (source.length === 0) { return new Uint8Array() }\n    var psz = 0\n        // Skip and count leading '1's.\n    var zeroes = 0\n    var length = 0\n    while (source[psz] === LEADER) {\n      zeroes++\n      psz++\n    }\n        // Allocate enough space in big-endian base256 representation.\n    var size = (((source.length - psz) * FACTOR) + 1) >>> 0 // log(58) / log(256), rounded up.\n    var b256 = new Uint8Array(size)\n        // Process the characters.\n    while (source[psz]) {\n            // Decode character\n      var carry = BASE_MAP[source.charCodeAt(psz)]\n            // Invalid character\n      if (carry === 255) { return }\n      var i = 0\n      for (var it3 = size - 1; (carry !== 0 || i < length) && (it3 !== -1); it3--, i++) {\n        carry += (BASE * b256[it3]) >>> 0\n        b256[it3] = (carry % 256) >>> 0\n        carry = (carry / 256) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      psz++\n    }\n        // Skip leading zeroes in b256.\n    var it4 = size - length\n    while (it4 !== size && b256[it4] === 0) {\n      it4++\n    }\n    var vch = new Uint8Array(zeroes + (size - it4))\n    var j = zeroes\n    while (it4 !== size) {\n      vch[j++] = b256[it4++]\n    }\n    return vch\n  }\n  function decode (string) {\n    var buffer = decodeUnsafe(string)\n    if (buffer) { return buffer }\n    throw new Error('Non-base' + BASE + ' character')\n  }\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  }\n}\nmodule.exports = base\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.c32ToB58 = exports.b58ToC32 = exports.c32addressDecode = exports.c32address = exports.versions = void 0;\nconst checksum_1 = require(\"./checksum\");\nconst base58check = require(\"./base58check\");\nconst utils_1 = require(\"@noble/hashes/utils\");\nexports.versions = {\n    mainnet: {\n        p2pkh: 22,\n        p2sh: 20, // 'M'\n    },\n    testnet: {\n        p2pkh: 26,\n        p2sh: 21, // 'N'\n    },\n};\n// address conversion : bitcoin to stacks\nconst ADDR_BITCOIN_TO_STACKS = {};\nADDR_BITCOIN_TO_STACKS[0] = exports.versions.mainnet.p2pkh;\nADDR_BITCOIN_TO_STACKS[5] = exports.versions.mainnet.p2sh;\nADDR_BITCOIN_TO_STACKS[111] = exports.versions.testnet.p2pkh;\nADDR_BITCOIN_TO_STACKS[196] = exports.versions.testnet.p2sh;\n// address conversion : stacks to bitcoin\nconst ADDR_STACKS_TO_BITCOIN = {};\nADDR_STACKS_TO_BITCOIN[exports.versions.mainnet.p2pkh] = 0;\nADDR_STACKS_TO_BITCOIN[exports.versions.mainnet.p2sh] = 5;\nADDR_STACKS_TO_BITCOIN[exports.versions.testnet.p2pkh] = 111;\nADDR_STACKS_TO_BITCOIN[exports.versions.testnet.p2sh] = 196;\n/**\n * Make a c32check address with the given version and hash160\n * The only difference between a c32check string and c32 address\n * is that the letter 'S' is pre-pended.\n * @param {number} version - the address version number\n * @param {string} hash160hex - the hash160 to encode (must be a hash160)\n * @returns {string} the address\n */\nfunction c32address(version, hash160hex) {\n    if (!hash160hex.match(/^[0-9a-fA-F]{40}$/)) {\n        throw new Error('Invalid argument: not a hash160 hex string');\n    }\n    const c32string = (0, checksum_1.c32checkEncode)(version, hash160hex);\n    return `S${c32string}`;\n}\nexports.c32address = c32address;\n/**\n * Decode a c32 address into its version and hash160\n * @param {string} c32addr - the c32check-encoded address\n * @returns {[number, string]} a tuple with the version and hash160\n */\nfunction c32addressDecode(c32addr) {\n    if (c32addr.length <= 5) {\n        throw new Error('Invalid c32 address: invalid length');\n    }\n    if (c32addr[0] != 'S') {\n        throw new Error('Invalid c32 address: must start with \"S\"');\n    }\n    return (0, checksum_1.c32checkDecode)(c32addr.slice(1));\n}\nexports.c32addressDecode = c32addressDecode;\n/*\n * Convert a base58check address to a c32check address.\n * Try to convert the version number if one is not given.\n * @param {string} b58check - the base58check encoded address\n * @param {number} version - the version number, if not inferred from the address\n * @returns {string} the c32 address with the given version number (or the\n *   semantically-equivalent c32 version number, if not given)\n */\nfunction b58ToC32(b58check, version = -1) {\n    const addrInfo = base58check.decode(b58check);\n    const hash160String = (0, utils_1.bytesToHex)(addrInfo.data);\n    const addrVersion = parseInt((0, utils_1.bytesToHex)(addrInfo.prefix), 16);\n    let stacksVersion;\n    if (version < 0) {\n        stacksVersion = addrVersion;\n        if (ADDR_BITCOIN_TO_STACKS[addrVersion] !== undefined) {\n            stacksVersion = ADDR_BITCOIN_TO_STACKS[addrVersion];\n        }\n    }\n    else {\n        stacksVersion = version;\n    }\n    return c32address(stacksVersion, hash160String);\n}\nexports.b58ToC32 = b58ToC32;\n/*\n * Convert a c32check address to a base58check address.\n * @param {string} c32string - the c32check address\n * @param {number} version - the version number, if not inferred from the address\n * @returns {string} the base58 address with the given version number (or the\n *    semantically-equivalent bitcoin version number, if not given)\n */\nfunction c32ToB58(c32string, version = -1) {\n    const addrInfo = c32addressDecode(c32string);\n    const stacksVersion = addrInfo[0];\n    const hash160String = addrInfo[1];\n    let bitcoinVersion;\n    if (version < 0) {\n        bitcoinVersion = stacksVersion;\n        if (ADDR_STACKS_TO_BITCOIN[stacksVersion] !== undefined) {\n            bitcoinVersion = ADDR_STACKS_TO_BITCOIN[stacksVersion];\n        }\n    }\n    else {\n        bitcoinVersion = version;\n    }\n    let prefix = bitcoinVersion.toString(16);\n    if (prefix.length === 1) {\n        prefix = `0${prefix}`;\n    }\n    return base58check.encode(hash160String, prefix);\n}\nexports.c32ToB58 = c32ToB58;\n", "/*\n * From https://github.com/wzbg/base58check\n * @Author: zyc\n * @Date:   2016-09-11 23:36:05\n */\n'use strict';\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.decode = exports.encode = void 0;\nconst sha256_1 = require(\"@noble/hashes/sha256\");\nconst utils_1 = require(\"@noble/hashes/utils\");\nconst basex = require(\"base-x\");\nconst ALPHABET = '**********************************************************';\nfunction encode(data, prefix = '00') {\n    const dataBytes = typeof data === 'string' ? (0, utils_1.hexToBytes)(data) : data;\n    const prefixBytes = typeof prefix === 'string' ? (0, utils_1.hexToBytes)(prefix) : data;\n    if (!(dataBytes instanceof Uint8Array) || !(prefixBytes instanceof Uint8Array)) {\n        throw new TypeError('Argument must be of type Uint8Array or string');\n    }\n    const checksum = (0, sha256_1.sha256)((0, sha256_1.sha256)(new Uint8Array([...prefixBytes, ...dataBytes])));\n    return basex(ALPHABET).encode([...prefixBytes, ...dataBytes, ...checksum.slice(0, 4)]);\n}\nexports.encode = encode;\nfunction decode(string) {\n    const bytes = basex(ALPHABET).decode(string);\n    const prefixBytes = bytes.slice(0, 1);\n    const dataBytes = bytes.slice(1, -4);\n    // todo: for better performance replace spread with `concatBytes` method\n    const checksum = (0, sha256_1.sha256)((0, sha256_1.sha256)(new Uint8Array([...prefixBytes, ...dataBytes])));\n    bytes.slice(-4).forEach((check, index) => {\n        if (check !== checksum[index]) {\n            throw new Error('Invalid checksum');\n        }\n    });\n    return { prefix: prefixBytes, data: dataBytes };\n}\nexports.decode = decode;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.c32checkDecode = exports.c32checkEncode = void 0;\nconst sha256_1 = require(\"@noble/hashes/sha256\");\nconst utils_1 = require(\"@noble/hashes/utils\");\nconst encoding_1 = require(\"./encoding\");\n/**\n * Get the c32check checksum of a hex-encoded string\n * @param {string} dataHex - the hex string\n * @returns {string} the c32 checksum, as a bin-encoded string\n */\nfunction c32checksum(dataHex) {\n    const dataHash = (0, sha256_1.sha256)((0, sha256_1.sha256)((0, utils_1.hexToBytes)(dataHex)));\n    const checksum = (0, utils_1.bytesToHex)(dataHash.slice(0, 4));\n    return checksum;\n}\n/**\n * Encode a hex string as a c32check string.  This is a lot like how\n * base58check works in Bitcoin-land, but this algorithm uses the\n * z-base-32 alphabet instead of the base58 alphabet.  The algorithm\n * is as follows:\n * * calculate the c32checksum of version + data\n * * c32encode version + data + c32checksum\n * @param {number} version - the version string (between 0 and 31)\n * @param {string} data - the data to encode\n * @returns {string} the c32check representation\n */\nfunction c32checkEncode(version, data) {\n    if (version < 0 || version >= 32) {\n        throw new Error('Invalid version (must be between 0 and 31)');\n    }\n    if (!data.match(/^[0-9a-fA-F]*$/)) {\n        throw new Error('Invalid data (not a hex string)');\n    }\n    data = data.toLowerCase();\n    if (data.length % 2 !== 0) {\n        data = `0${data}`;\n    }\n    let versionHex = version.toString(16);\n    if (versionHex.length === 1) {\n        versionHex = `0${versionHex}`;\n    }\n    const checksumHex = c32checksum(`${versionHex}${data}`);\n    const c32str = (0, encoding_1.c32encode)(`${data}${checksumHex}`);\n    return `${encoding_1.c32[version]}${c32str}`;\n}\nexports.c32checkEncode = c32checkEncode;\n/*\n * Decode a c32check string back into its version and data payload.  This is\n * a lot like how base58check works in Bitcoin-land, but this algorithm uses\n * the z-base-32 alphabet instead of the base58 alphabet.  The algorithm\n * is as follows:\n * * extract the version, data, and checksum\n * * verify the checksum matches c32checksum(version + data)\n * * return data\n * @param {string} c32data - the c32check-encoded string\n * @returns {array} [version (number), data (string)].  The returned data\n * will be a hex string.  Throws an exception if the checksum does not match.\n */\nfunction c32checkDecode(c32data) {\n    c32data = (0, encoding_1.c32normalize)(c32data);\n    const dataHex = (0, encoding_1.c32decode)(c32data.slice(1));\n    const versionChar = c32data[0];\n    const version = encoding_1.c32.indexOf(versionChar);\n    const checksum = dataHex.slice(-8);\n    let versionHex = version.toString(16);\n    if (versionHex.length === 1) {\n        versionHex = `0${versionHex}`;\n    }\n    if (c32checksum(`${versionHex}${dataHex.substring(0, dataHex.length - 8)}`) !== checksum) {\n        throw new Error('Invalid c32check string: checksum mismatch');\n    }\n    return [version, dataHex.substring(0, dataHex.length - 8)];\n}\nexports.c32checkDecode = c32checkDecode;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.c32decode = exports.c32normalize = exports.c32encode = exports.c32 = void 0;\nconst utils_1 = require(\"@noble/hashes/utils\");\nexports.c32 = '0123456789ABCDEFGHJKMNPQRSTVWXYZ';\nconst hex = '0123456789abcdef';\n/**\n * Encode a hex string as a c32 string.  Note that the hex string is assumed\n * to be big-endian (and the resulting c32 string will be as well).\n * @param {string} inputHex - the input to encode\n * @param {number} minLength - the minimum length of the c32 string\n * @returns {string} the c32check-encoded representation of the data, as a string\n */\nfunction c32encode(inputHex, minLength) {\n    // must be hex\n    if (!inputHex.match(/^[0-9a-fA-F]*$/)) {\n        throw new Error('Not a hex-encoded string');\n    }\n    if (inputHex.length % 2 !== 0) {\n        inputHex = `0${inputHex}`;\n    }\n    inputHex = inputHex.toLowerCase();\n    let res = [];\n    let carry = 0;\n    for (let i = inputHex.length - 1; i >= 0; i--) {\n        if (carry < 4) {\n            const currentCode = hex.indexOf(inputHex[i]) >> carry;\n            let nextCode = 0;\n            if (i !== 0) {\n                nextCode = hex.indexOf(inputHex[i - 1]);\n            }\n            // carry = 0, nextBits is 1, carry = 1, nextBits is 2\n            const nextBits = 1 + carry;\n            const nextLowBits = nextCode % (1 << nextBits) << (5 - nextBits);\n            const curC32Digit = exports.c32[currentCode + nextLowBits];\n            carry = nextBits;\n            res.unshift(curC32Digit);\n        }\n        else {\n            carry = 0;\n        }\n    }\n    let C32leadingZeros = 0;\n    for (let i = 0; i < res.length; i++) {\n        if (res[i] !== '0') {\n            break;\n        }\n        else {\n            C32leadingZeros++;\n        }\n    }\n    res = res.slice(C32leadingZeros);\n    const zeroPrefix = new TextDecoder().decode((0, utils_1.hexToBytes)(inputHex)).match(/^\\u0000*/);\n    const numLeadingZeroBytesInHex = zeroPrefix ? zeroPrefix[0].length : 0;\n    for (let i = 0; i < numLeadingZeroBytesInHex; i++) {\n        res.unshift(exports.c32[0]);\n    }\n    if (minLength) {\n        const count = minLength - res.length;\n        for (let i = 0; i < count; i++) {\n            res.unshift(exports.c32[0]);\n        }\n    }\n    return res.join('');\n}\nexports.c32encode = c32encode;\n/*\n * Normalize a c32 string\n * @param {string} c32input - the c32-encoded input string\n * @returns {string} the canonical representation of the c32 input string\n */\nfunction c32normalize(c32input) {\n    // must be upper-case\n    // replace all O's with 0's\n    // replace all I's and L's with 1's\n    return c32input.toUpperCase().replace(/O/g, '0').replace(/L|I/g, '1');\n}\nexports.c32normalize = c32normalize;\n/*\n * Decode a c32 string back into a hex string.  Note that the c32 input\n * string is assumed to be big-endian (and the resulting hex string will\n * be as well).\n * @param {string} c32input - the c32-encoded input to decode\n * @param {number} minLength - the minimum length of the output hex string (in bytes)\n * @returns {string} the hex-encoded representation of the data, as a string\n */\nfunction c32decode(c32input, minLength) {\n    c32input = c32normalize(c32input);\n    // must result in a c32 string\n    if (!c32input.match(`^[${exports.c32}]*$`)) {\n        throw new Error('Not a c32-encoded string');\n    }\n    const zeroPrefix = c32input.match(`^${exports.c32[0]}*`);\n    const numLeadingZeroBytes = zeroPrefix ? zeroPrefix[0].length : 0;\n    let res = [];\n    let carry = 0;\n    let carryBits = 0;\n    for (let i = c32input.length - 1; i >= 0; i--) {\n        if (carryBits === 4) {\n            res.unshift(hex[carry]);\n            carryBits = 0;\n            carry = 0;\n        }\n        const currentCode = exports.c32.indexOf(c32input[i]) << carryBits;\n        const currentValue = currentCode + carry;\n        const currentHexDigit = hex[currentValue % 16];\n        carryBits += 1;\n        carry = currentValue >> 4;\n        if (carry > 1 << carryBits) {\n            throw new Error('Panic error in decoding.');\n        }\n        res.unshift(currentHexDigit);\n    }\n    // one last carry\n    res.unshift(hex[carry]);\n    if (res.length % 2 === 1) {\n        res.unshift('0');\n    }\n    let hexLeadingZeros = 0;\n    for (let i = 0; i < res.length; i++) {\n        if (res[i] !== '0') {\n            break;\n        }\n        else {\n            hexLeadingZeros++;\n        }\n    }\n    res = res.slice(hexLeadingZeros - (hexLeadingZeros % 2));\n    let hexStr = res.join('');\n    for (let i = 0; i < numLeadingZeroBytes; i++) {\n        hexStr = `00${hexStr}`;\n    }\n    if (minLength) {\n        const count = minLength * 2 - hexStr.length;\n        for (let i = 0; i < count; i += 2) {\n            hexStr = `00${hexStr}`;\n        }\n    }\n    return hexStr;\n}\nexports.c32decode = c32decode;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.b58ToC32 = exports.c32ToB58 = exports.versions = exports.c32normalize = exports.c32addressDecode = exports.c32address = exports.c32checkDecode = exports.c32checkEncode = exports.c32decode = exports.c32encode = void 0;\nconst encoding_1 = require(\"./encoding\");\nObject.defineProperty(exports, \"c32encode\", { enumerable: true, get: function () { return encoding_1.c32encode; } });\nObject.defineProperty(exports, \"c32decode\", { enumerable: true, get: function () { return encoding_1.c32decode; } });\nObject.defineProperty(exports, \"c32normalize\", { enumerable: true, get: function () { return encoding_1.c32normalize; } });\nconst checksum_1 = require(\"./checksum\");\nObject.defineProperty(exports, \"c32checkEncode\", { enumerable: true, get: function () { return checksum_1.c32checkEncode; } });\nObject.defineProperty(exports, \"c32checkDecode\", { enumerable: true, get: function () { return checksum_1.c32checkDecode; } });\nconst address_1 = require(\"./address\");\nObject.defineProperty(exports, \"c32address\", { enumerable: true, get: function () { return address_1.c32address; } });\nObject.defineProperty(exports, \"c32addressDecode\", { enumerable: true, get: function () { return address_1.c32addressDecode; } });\nObject.defineProperty(exports, \"c32ToB58\", { enumerable: true, get: function () { return address_1.c32ToB58; } });\nObject.defineProperty(exports, \"b58ToC32\", { enumerable: true, get: function () { return address_1.b58ToC32; } });\nObject.defineProperty(exports, \"versions\", { enumerable: true, get: function () { return address_1.versions; } });\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "number", "n", "Number", "isSafeInteger", "Error", "bool", "b", "bytes", "lengths", "Uint8Array", "TypeError", "length", "includes", "hash", "create", "outputLen", "blockLen", "exists", "instance", "checkFinished", "destroyed", "finished", "output", "out", "min", "Object", "defineProperty", "value", "assert", "SHA2", "_assert_js_1", "utils_js_1", "Hash", "constructor", "padOffset", "isLE", "super", "pos", "buffer", "view", "createView", "update", "data", "default", "len", "toBytes", "take", "Math", "set", "subarray", "process", "dataView", "roundClean", "digestInto", "fill", "i", "byteOffset", "setBigUint64", "_32n", "BigInt", "_u32_max", "wh", "wl", "h", "l", "setUint32", "oview", "get", "for<PERSON>ach", "v", "digest", "res", "slice", "destroy", "_cloneInto", "to", "crypto", "node", "undefined", "web", "self", "sha256", "_sha2_js_1", "Maj", "a", "c", "SHA256_K", "Uint32Array", "IV", "SHA256_W", "SHA256", "A", "B", "C", "D", "E", "F", "G", "H", "offset", "getUint32", "W15", "W2", "s0", "rotr", "s1", "T1", "T2", "wrapConstructor", "randomBytes", "wrapConstructorWithOpts", "checkOpts", "concatBytes", "utf8ToBytes", "asyncLoop", "nextTick", "hexToBytes", "bytesToHex", "u32", "u8", "crypto_1", "arr", "byteLength", "floor", "DataView", "word", "shift", "hexes", "Array", "from", "toString", "padStart", "str", "TextEncoder", "encode", "uint8a", "hex", "array", "j", "hexByte", "byte", "parseInt", "isNaN", "async", "iters", "tick", "cb", "ts", "Date", "now", "diff", "arrays", "every", "reduce", "result", "pad", "clone", "defaults", "opts", "obj", "prototype", "call", "assign", "hashConstructor", "hashC", "message", "tmp", "hashCons", "msg", "bytesLength", "getRandomValues", "ALPHABET", "BASE_MAP", "x", "char<PERSON>t", "xc", "charCodeAt", "BASE", "LEADER", "FACTOR", "log", "iFACTOR", "decodeUnsafe", "source", "psz", "zeroes", "size", "b256", "carry", "it3", "it4", "vch", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "isArray", "pbegin", "pend", "b58", "it1", "it2", "repeat", "decode", "string", "c32ToB58", "b58ToC32", "c32addressDecode", "c32address", "versions", "checksum_1", "base58check", "utils_1", "mainnet", "p2pkh", "p2sh", "testnet", "ADDR_BITCOIN_TO_STACKS", "ADDR_STACKS_TO_BITCOIN", "version", "hash160hex", "match", "c32checkEncode", "c32addr", "c32checkDecode", "b58check", "addrInfo", "hash160String", "addrVersion", "prefix", "stacksVersion", "c32string", "bitcoinVersion", "sha256_1", "basex", "dataBytes", "prefixBytes", "checksum", "check", "index", "encoding_1", "c32checksum", "dataHex", "dataHash", "toLowerCase", "versionHex", "checksumHex", "c32str", "c32encode", "c32", "c32data", "c32normalize", "c32decode", "versionChar", "indexOf", "substring", "c32input", "toUpperCase", "replace", "inputHex", "<PERSON><PERSON><PERSON><PERSON>", "currentCode", "nextCode", "nextBits", "nextLowBits", "curC32Digit", "unshift", "C32leadingZeros", "zeroPrefix", "TextDecoder", "numLeadingZeroBytesInHex", "count", "join", "numLeadingZeroBytes", "carryBits", "currentValue", "currentHexDigit", "hexLeadingZeros", "hexStr", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "enumerable", "address_1"], "sourceRoot": ""}