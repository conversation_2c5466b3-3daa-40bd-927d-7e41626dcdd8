!function(t){!function(e){var r="undefined"!=typeof globalThis&&globalThis||void 0!==t&&t||"undefined"!=typeof global&&global||{},o="URLSearchParams"in r,n="Symbol"in r&&"iterator"in Symbol,i="FileReader"in r&&"Blob"in r&&function(){try{return new Blob,!0}catch(t){return!1}}(),s="FormData"in r,a="ArrayBuffer"in r;if(a)var h=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],u=ArrayBuffer.isView||function(t){return t&&h.indexOf(Object.prototype.toString.call(t))>-1};function f(t){if("string"!=typeof t&&(t=String(t)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(t)||""===t)throw new TypeError('Invalid character in header field name: "'+t+'"');return t.toLowerCase()}function d(t){return"string"!=typeof t&&(t=String(t)),t}function c(t){var e={next:function(){var e=t.shift();return{done:void 0===e,value:e}}};return n&&(e[Symbol.iterator]=function(){return e}),e}function y(t){this.map={},t instanceof y?t.forEach((function(t,e){this.append(e,t)}),this):Array.isArray(t)?t.forEach((function(t){if(2!=t.length)throw new TypeError("Headers constructor: expected name/value pair to be length 2, found"+t.length);this.append(t[0],t[1])}),this):t&&Object.getOwnPropertyNames(t).forEach((function(e){this.append(e,t[e])}),this)}function l(t){if(!t._noBody)return t.bodyUsed?Promise.reject(new TypeError("Already read")):void(t.bodyUsed=!0)}function p(t){return new Promise((function(e,r){t.onload=function(){e(t.result)},t.onerror=function(){r(t.error)}}))}function b(t){var e=new FileReader,r=p(e);return e.readAsArrayBuffer(t),r}function m(t){if(t.slice)return t.slice(0);var e=new Uint8Array(t.byteLength);return e.set(new Uint8Array(t)),e.buffer}function w(){return this.bodyUsed=!1,this._initBody=function(t){var e;this.bodyUsed=this.bodyUsed,this._bodyInit=t,t?"string"==typeof t?this._bodyText=t:i&&Blob.prototype.isPrototypeOf(t)?this._bodyBlob=t:s&&FormData.prototype.isPrototypeOf(t)?this._bodyFormData=t:o&&URLSearchParams.prototype.isPrototypeOf(t)?this._bodyText=t.toString():a&&i&&((e=t)&&DataView.prototype.isPrototypeOf(e))?(this._bodyArrayBuffer=m(t.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):a&&(ArrayBuffer.prototype.isPrototypeOf(t)||u(t))?this._bodyArrayBuffer=m(t):this._bodyText=t=Object.prototype.toString.call(t):(this._noBody=!0,this._bodyText=""),this.headers.get("content-type")||("string"==typeof t?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):o&&URLSearchParams.prototype.isPrototypeOf(t)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},i&&(this.blob=function(){var t=l(this);if(t)return t;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))}),this.arrayBuffer=function(){if(this._bodyArrayBuffer){var t=l(this);return t||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer))}if(i)return this.blob().then(b);throw new Error("could not read as ArrayBuffer")},this.text=function(){var t,e,r,o,n,i=l(this);if(i)return i;if(this._bodyBlob)return t=this._bodyBlob,e=new FileReader,r=p(e),o=/charset=([A-Za-z0-9_-]+)/.exec(t.type),n=o?o[1]:"utf-8",e.readAsText(t,n),r;if(this._bodyArrayBuffer)return Promise.resolve(function(t){for(var e=new Uint8Array(t),r=new Array(e.length),o=0;o<e.length;o++)r[o]=String.fromCharCode(e[o]);return r.join("")}(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},s&&(this.formData=function(){return this.text().then(A)}),this.json=function(){return this.text().then(JSON.parse)},this}y.prototype.append=function(t,e){t=f(t),e=d(e);var r=this.map[t];this.map[t]=r?r+", "+e:e},y.prototype.delete=function(t){delete this.map[f(t)]},y.prototype.get=function(t){return t=f(t),this.has(t)?this.map[t]:null},y.prototype.has=function(t){return this.map.hasOwnProperty(f(t))},y.prototype.set=function(t,e){this.map[f(t)]=d(e)},y.prototype.forEach=function(t,e){for(var r in this.map)this.map.hasOwnProperty(r)&&t.call(e,this.map[r],r,this)},y.prototype.keys=function(){var t=[];return this.forEach((function(e,r){t.push(r)})),c(t)},y.prototype.values=function(){var t=[];return this.forEach((function(e){t.push(e)})),c(t)},y.prototype.entries=function(){var t=[];return this.forEach((function(e,r){t.push([r,e])})),c(t)},n&&(y.prototype[Symbol.iterator]=y.prototype.entries);var E=["CONNECT","DELETE","GET","HEAD","OPTIONS","PATCH","POST","PUT","TRACE"];function v(t,e){if(!(this instanceof v))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');var o,n,i=(e=e||{}).body;if(t instanceof v){if(t.bodyUsed)throw new TypeError("Already read");this.url=t.url,this.credentials=t.credentials,e.headers||(this.headers=new y(t.headers)),this.method=t.method,this.mode=t.mode,this.signal=t.signal,i||null==t._bodyInit||(i=t._bodyInit,t.bodyUsed=!0)}else this.url=String(t);if(this.credentials=e.credentials||this.credentials||"same-origin",!e.headers&&this.headers||(this.headers=new y(e.headers)),this.method=(o=e.method||this.method||"GET",n=o.toUpperCase(),E.indexOf(n)>-1?n:o),this.mode=e.mode||this.mode||null,this.signal=e.signal||this.signal||function(){if("AbortController"in r)return(new AbortController).signal}(),this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&i)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(i),!("GET"!==this.method&&"HEAD"!==this.method||"no-store"!==e.cache&&"no-cache"!==e.cache)){var s=/([?&])_=[^&]*/;if(s.test(this.url))this.url=this.url.replace(s,"$1_="+(new Date).getTime());else{this.url+=(/\?/.test(this.url)?"&":"?")+"_="+(new Date).getTime()}}}function A(t){var e=new FormData;return t.trim().split("&").forEach((function(t){if(t){var r=t.split("="),o=r.shift().replace(/\+/g," "),n=r.join("=").replace(/\+/g," ");e.append(decodeURIComponent(o),decodeURIComponent(n))}})),e}function g(t,e){if(!(this instanceof g))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');if(e||(e={}),this.type="default",this.status=void 0===e.status?200:e.status,this.status<200||this.status>599)throw new RangeError("Failed to construct 'Response': The status provided (0) is outside the range [200, 599].");this.ok=this.status>=200&&this.status<300,this.statusText=void 0===e.statusText?"":""+e.statusText,this.headers=new y(e.headers),this.url=e.url||"",this._initBody(t)}v.prototype.clone=function(){return new v(this,{body:this._bodyInit})},w.call(v.prototype),w.call(g.prototype),g.prototype.clone=function(){return new g(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new y(this.headers),url:this.url})},g.error=function(){var t=new g(null,{status:200,statusText:""});return t.ok=!1,t.status=0,t.type="error",t};var T=[301,302,303,307,308];g.redirect=function(t,e){if(-1===T.indexOf(e))throw new RangeError("Invalid status code");return new g(null,{status:e,headers:{location:t}})},e.DOMException=r.DOMException;try{new e.DOMException}catch(t){e.DOMException=function(t,e){this.message=t,this.name=e;var r=Error(t);this.stack=r.stack},e.DOMException.prototype=Object.create(Error.prototype),e.DOMException.prototype.constructor=e.DOMException}function _(t,o){return new Promise((function(n,s){var h=new v(t,o);if(h.signal&&h.signal.aborted)return s(new e.DOMException("Aborted","AbortError"));var u=new XMLHttpRequest;function c(){u.abort()}if(u.onload=function(){var t,e,r={statusText:u.statusText,headers:(t=u.getAllResponseHeaders()||"",e=new y,t.replace(/\r?\n[\t ]+/g," ").split("\r").map((function(t){return 0===t.indexOf("\n")?t.substr(1,t.length):t})).forEach((function(t){var r=t.split(":"),o=r.shift().trim();if(o){var n=r.join(":").trim();try{e.append(o,n)}catch(t){console.warn("Response "+t.message)}}})),e)};0===h.url.indexOf("file://")&&(u.status<200||u.status>599)?r.status=200:r.status=u.status,r.url="responseURL"in u?u.responseURL:r.headers.get("X-Request-URL");var o="response"in u?u.response:u.responseText;setTimeout((function(){n(new g(o,r))}),0)},u.onerror=function(){setTimeout((function(){s(new TypeError("Network request failed"))}),0)},u.ontimeout=function(){setTimeout((function(){s(new TypeError("Network request timed out"))}),0)},u.onabort=function(){setTimeout((function(){s(new e.DOMException("Aborted","AbortError"))}),0)},u.open(h.method,function(t){try{return""===t&&r.location.href?r.location.href:t}catch(e){return t}}(h.url),!0),"include"===h.credentials?u.withCredentials=!0:"omit"===h.credentials&&(u.withCredentials=!1),"responseType"in u&&(i?u.responseType="blob":a&&(u.responseType="arraybuffer")),o&&"object"==typeof o.headers&&!(o.headers instanceof y||r.Headers&&o.headers instanceof r.Headers)){var l=[];Object.getOwnPropertyNames(o.headers).forEach((function(t){l.push(f(t)),u.setRequestHeader(t,d(o.headers[t]))})),h.headers.forEach((function(t,e){-1===l.indexOf(e)&&u.setRequestHeader(e,t)}))}else h.headers.forEach((function(t,e){u.setRequestHeader(e,t)}));h.signal&&(h.signal.addEventListener("abort",c),u.onreadystatechange=function(){4===u.readyState&&h.signal.removeEventListener("abort",c)}),u.send(void 0===h._bodyInit?null:h._bodyInit)}))}_.polyfill=!0,r.fetch||(r.fetch=_,r.Headers=y,r.Request=v,r.Response=g),e.Headers=y,e.Request=v,e.Response=g,e.fetch=_,Object.defineProperty(e,"__esModule",{value:!0})}({})}("undefined"!=typeof self?self:this);
//# sourceMappingURL=cross-fetch.js.map
